#!/usr/bin/env python3
"""
Test script for export and persistence functionality.
"""

import sys
from pathlib import Path
import logging
import json

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_project_serializer():
    """Test project serializer functionality."""
    print("Testing project serializer...")
    
    try:
        from persistence.project_serializer import ProjectSerializer
        from core.geometry.shapes import Template, Sheet, Cut, Point
        from core.cutting.metadata import ProjectMetrics
        from shapely.geometry import box
        
        # Create test objects
        template = Template(polygon=box(0, 0, 100, 50))
        sheet = Sheet(width=200, height=100)
        
        # Create a simple cut (mock)
        cut = Cut(
            template_id=template.id,
            sheet_id=sheet.id,
            cut_polygon=box(10, 10, 110, 60),
            cut_lines=[],
            metadata={'test': True}
        )
        
        project_metrics = ProjectMetrics(
            total_cuts=1,
            total_cut_area_mm2=5000.0,
            total_waste_area_mm2=1000.0,
            average_utilization=0.8,
            average_efficiency=85.0,
            sheets_used=1,
            templates_cut=1
        )
        
        # Test serialization
        serializer = ProjectSerializer()
        
        project_data = serializer.serialize_project(
            [template], [sheet], [cut], project_metrics,
            metadata={'test_project': True}
        )
        
        print("✓ Project serialization successful")
        
        # Test validation
        is_valid = serializer.validate_project_data(project_data)
        if is_valid:
            print("✓ Project data validation successful")
        else:
            print("✗ Project data validation failed")
            return False
        
        # Test deserialization
        restored_data = serializer.deserialize_project(project_data)
        
        print("✓ Project deserialization successful")
        print(f"  Restored: {len(restored_data['templates'])} templates, "
               f"{len(restored_data['sheets'])} sheets, {len(restored_data['cuts'])} cuts")
        
        return True
        
    except Exception as e:
        print(f"✗ Project serializer test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_json_handler():
    """Test JSON project handler."""
    print("Testing JSON project handler...")
    
    try:
        from persistence.json_handler import JSONProjectHandler
        from core.geometry.shapes import Template, Sheet, Cut
        from core.cutting.metadata import ProjectMetrics
        from shapely.geometry import box
        
        # Create test objects
        template = Template(polygon=box(0, 0, 100, 50), metadata={'name': 'Test Template'})
        sheet = Sheet(width=200, height=100, metadata={'name': 'Test Sheet'})
        
        cut = Cut(
            template_id=template.id,
            sheet_id=sheet.id,
            cut_polygon=box(10, 10, 110, 60),
            cut_lines=[],
            metadata={'test_cut': True}
        )
        
        project_metrics = ProjectMetrics(
            total_cuts=1,
            total_cut_area_mm2=5000.0,
            total_waste_area_mm2=1000.0,
            average_utilization=0.8,
            average_efficiency=85.0,
            sheets_used=1,
            templates_cut=1
        )
        
        # Test save
        handler = JSONProjectHandler()
        test_file = project_root / "test_project_handler.json"
        
        success = handler.save_project(
            str(test_file), [template], [sheet], [cut], project_metrics,
            metadata={'test_handler': True}
        )
        
        if not success:
            print("✗ Project save failed")
            return False
        
        print("✓ Project save successful")
        
        # Test load
        loaded_data = handler.load_project(str(test_file))
        
        if not loaded_data:
            print("✗ Project load failed")
            return False
        
        print("✓ Project load successful")
        
        # Test project summary
        summary = handler.export_project_summary(str(test_file))
        if summary:
            print("✓ Project summary export successful")
            print(f"  Summary: {summary['statistics']}")
        else:
            print("⚠ Project summary export failed")
        
        # Clean up
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ JSON handler test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_exporter():
    """Test PDF exporter functionality."""
    print("Testing PDF exporter...")
    
    try:
        from export.pdf.pdf_exporter import PDFExporter
        from core.geometry.shapes import Template, Sheet, Cut
        from core.cutting.metadata import ProjectMetrics
        from shapely.geometry import box
        
        # Create test objects
        template = Template(
            polygon=box(0, 0, 200, 100), 
            metadata={'name': 'Rectangle Template', 'type': 'standard'}
        )
        
        sheet = Sheet(
            width=1200, height=2400,
            metadata={'name': 'Standard Sheet', 'material': 'Drywall'}
        )
        
        cut = Cut(
            template_id=template.id,
            sheet_id=sheet.id,
            cut_polygon=box(100, 100, 300, 200),
            cut_lines=[],
            metadata={'cut_area_mm2': 20000, 'sheet_utilization': 0.7}
        )
        
        project_metrics = ProjectMetrics(
            total_cuts=1,
            total_cut_area_mm2=20000.0,
            total_waste_area_mm2=5000.0,
            total_cut_length_mm=800.0,
            average_utilization=0.7,
            average_efficiency=85.0,
            sheets_used=1,
            templates_cut=1
        )
        
        # Test PDF export
        exporter = PDFExporter()
        test_pdf = project_root / "test_export.pdf"
        
        success = exporter.export_project_report(
            str(test_pdf), [template], [sheet], [cut], project_metrics
        )
        
        if not success:
            print("✗ PDF export failed")
            return False
        
        print("✓ PDF export successful")
        
        # Check if file was created
        if test_pdf.exists():
            file_size = test_pdf.stat().st_size
            print(f"  PDF file created: {file_size} bytes")
            
            # Clean up
            test_pdf.unlink()
        else:
            print("⚠ PDF file was not created")
        
        return True
        
    except Exception as e:
        print(f"✗ PDF exporter test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_sheet_report_generator():
    """Test sheet report generator."""
    print("Testing sheet report generator...")
    
    try:
        from export.pdf.sheet_report import SheetReportGenerator
        from core.geometry.shapes import Template, Sheet, Cut
        from shapely.geometry import box
        import tkinter as tk
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # Create test objects
        sheet = Sheet(width=1200, height=2400, metadata={'name': 'Test Sheet'})
        template = Template(polygon=box(0, 0, 200, 100), metadata={'name': 'Test Template'})
        
        cut = Cut(
            template_id=template.id,
            sheet_id=sheet.id,
            cut_polygon=box(100, 100, 300, 200),
            cut_lines=[],
            metadata={}
        )
        
        # Create a test PDF canvas
        test_pdf = project_root / "test_sheet_report.pdf"
        pdf_canvas = canvas.Canvas(str(test_pdf), pagesize=A4)
        
        # Test sheet report generation
        generator = SheetReportGenerator()
        
        width, height = generator.generate_sheet_report(
            pdf_canvas, sheet, [cut], [template],
            x_offset=50, y_offset=50,
            max_width=400, max_height=300
        )
        
        pdf_canvas.save()
        
        if width > 0 and height > 0:
            print(f"✓ Sheet report generated: {width:.1f} x {height:.1f}")
        else:
            print("✗ Sheet report generation failed")
            return False
        
        # Clean up
        if test_pdf.exists():
            test_pdf.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Sheet report generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_assembly_report_generator():
    """Test assembly report generator."""
    print("Testing assembly report generator...")
    
    try:
        from export.pdf.assembly_report import AssemblyReportGenerator
        from core.geometry.shapes import Template, Sheet, Cut
        from shapely.geometry import box
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # Create test objects
        template = Template(
            polygon=box(0, 0, 200, 100), 
            metadata={'name': 'Assembly Template'}
        )
        
        sheet = Sheet(width=1200, height=2400, metadata={'name': 'Source Sheet'})
        
        cut = Cut(
            template_id=template.id,
            sheet_id=sheet.id,
            cut_polygon=box(100, 100, 300, 200),
            cut_lines=[],
            metadata={'cut_area_mm2': 20000}
        )
        
        # Create a test PDF canvas
        test_pdf = project_root / "test_assembly_report.pdf"
        pdf_canvas = canvas.Canvas(str(test_pdf), pagesize=A4)
        
        # Test assembly report generation
        generator = AssemblyReportGenerator()
        
        width, height = generator.generate_assembly_report(
            pdf_canvas, template, [cut], [sheet],
            x_offset=50, y_offset=50,
            max_width=400, max_height=500
        )
        
        pdf_canvas.save()
        
        if width > 0 and height > 0:
            print(f"✓ Assembly report generated: {width:.1f} x {height:.1f}")
        else:
            print("✗ Assembly report generation failed")
            return False
        
        # Clean up
        if test_pdf.exists():
            test_pdf.unlink()
        
        return True
        
    except Exception as e:
        print(f"✗ Assembly report generator test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all export and persistence tests."""
    print("=== Export and Persistence Tests ===\n")
    
    tests = [
        test_project_serializer,
        test_json_handler,
        test_pdf_exporter,
        test_sheet_report_generator,
        test_assembly_report_generator
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print(f"=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All export and persistence tests passed!")
        print("\nPhase 3 implementation is complete:")
        print("- ✅ ProjectSerializer with WKT geometry handling")
        print("- ✅ JSONProjectHandler with backup support")
        print("- ✅ PDFExporter with comprehensive reports")
        print("- ✅ SheetReportGenerator with visual layouts")
        print("- ✅ AssemblyReportGenerator with instructions")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
