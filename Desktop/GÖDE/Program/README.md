# Drywall Optimization Application

A desktop application for optimizing drywall cutting by fitting DXF-exported templates onto available sheet stock.

## Features

- **DXF Import**: Read single-layer DXF files and extract closed polylines as templates
- **User-defined Inventory**: Specify any number of sheet sizes and quantities at runtime
- **Configurable Snapping**: Set snap-tolerance for edge-and-corner alignment
- **Drag-and-drop Canvas**: Display templates and sheets in true scale with drag, drop, rotate, and snap
- **Single-step Clipping**: Cut templates from sheets with automatic remainder management
- **Cut Metadata Tracking**: Track cut IDs, sheet origins, coordinates, and segment lengths
- **PDF Export**: Generate vector-based reports for sheets and template assembly
- **Undo Functionality**: Single-level undo for cut operations
- **Project Save/Load**: Save and reload project state as JSON

## Project Structure

```
drywall_optimizer/
├── main.py                 # Application entry point
├── requirements.txt        # Dependencies
├── setup.py               # Installation script
├── config/
│   └── settings.json      # Default configuration
├── core/
│   ├── geometry/          # 2D geometry operations
│   │   ├── shapes.py      # Point, Template, Sheet, Cut classes
│   │   ├── operations.py  # Clipping, snapping operations
│   │   └── spatial.py     # Spatial indexing
│   ├── dxf_import/        # DXF file processing
│   │   ├── parser.py      # DXF file parsing
│   │   └── converter.py   # DXF to internal format
│   ├── cutting/           # Cutting logic (to be implemented)
│   └── project/           # Project state management (to be implemented)
├── ui/
│   ├── main_window.py     # Main application window
│   ├── canvas/            # Interactive canvas (to be implemented)
│   ├── panels/            # UI panels (to be implemented)
│   └── dialogs/           # Modal dialogs (to be implemented)
├── export/
│   └── pdf/               # PDF report generation (to be implemented)
├── persistence/           # Save/load functionality (to be implemented)
└── utils/
    ├── validation.py      # Input validation
    └── units.py          # Unit conversion utilities
```

## Installation

1. **Clone or download** the project to your local machine

2. **Install Python dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Run the application**:
   ```bash
   python main.py
   ```

## Dependencies

- **ezdxf**: DXF file processing
- **shapely**: 2D geometry operations and polygon clipping
- **reportlab**: PDF generation for reports
- **tkinter**: GUI framework (built-in with Python)
- **rtree**: Spatial indexing for performance
- **pillow**: Image processing support

## Current Status

This is the initial project structure with core components implemented:

### ✅ Completed
- Project structure and configuration
- Core geometry classes (Point, Template, Sheet, Cut)
- DXF import functionality with ezdxf
- Basic geometry operations (clipping, snapping)
- Spatial indexing for performance
- Unit conversion utilities
- Input validation
- Main window framework

### 🚧 In Progress
- Interactive canvas with zoom/pan
- Materials and properties panels
- Dialog windows for import/add operations

### 📋 To Do
- Cutting logic and algorithms
- Undo functionality
- PDF export reports
- Project save/load
- Complete UI implementation
- Testing and optimization

## Configuration

The application uses `config/settings.json` for default settings including:
- Window dimensions and layout
- Canvas appearance and behavior
- Snapping tolerances
- Material defaults
- Export settings
- Performance options

## Usage

1. **Start the application** by running `python main.py`
2. **Import DXF files** containing closed polylines as templates
3. **Add sheet materials** with custom dimensions and quantities
4. **Drag and position** templates on sheets using the canvas
5. **Cut templates** by pressing Enter when positioned
6. **Export PDF reports** showing cut layouts and assembly instructions

## Development

To continue development:

1. **Implement remaining UI components** in the `ui/` directory
2. **Add cutting logic** in `core/cutting/`
3. **Create PDF export** functionality in `export/pdf/`
4. **Add project persistence** in `persistence/`
5. **Write tests** in the `tests/` directory

## Architecture

The application follows a modular architecture:
- **Core**: Business logic and data models
- **UI**: User interface components
- **Utils**: Shared utilities and helpers
- **Export**: Report generation
- **Persistence**: Data storage and retrieval

## License

[Add your license information here]

## Contributing

[Add contribution guidelines here]
