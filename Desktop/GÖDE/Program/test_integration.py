#!/usr/bin/env python3
"""
Integration test for the complete drywall optimization application.

This test verifies that all phases work together correctly:
- Phase 1: Canvas with polygon rendering and drag-and-drop
- Phase 2: Cutting engine with metadata tracking
- Phase 3: Export and persistence functionality
"""

import sys
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

def test_complete_workflow():
    """Test the complete application workflow."""
    print("Testing complete application workflow...")
    
    try:
        # Import all major components
        from core.geometry.shapes import Template, Sheet, Cut, Point
        from core.cutting.engine import CuttingEngine
        from core.cutting.metadata import MetadataTracker
        from export.pdf import PDFExporter
        from persistence import JSONProjectHandler
        from shapely.geometry import box, Polygon
        
        print("✓ All major components imported successfully")
        
        # Phase 1: Create geometry objects
        template = Template(
            polygon=box(0, 0, 200, 100),
            metadata={'name': 'Test Template', 'type': 'rectangle'}
        )
        
        sheet = Sheet(
            width=1200, height=2400,
            metadata={'name': 'Standard Sheet', 'material': 'Drywall 12.5mm'}
        )
        
        print("✓ Phase 1: Geometry objects created")
        
        # Phase 2: Perform cutting operation
        cutting_engine = CuttingEngine()
        metadata_tracker = MetadataTracker()
        
        # Position template on sheet
        positioned_template = template.move_to(Point(100, 100))
        
        # Perform cut
        result = cutting_engine.cut_template_from_sheet(positioned_template, sheet)
        
        if not result.success:
            print(f"✗ Cutting operation failed: {result.message}")
            return False
        
        # Track metadata
        waste_area = sum(s.polygon.area for s in result.remainder_sheets)
        metrics = metadata_tracker.record_cut(result.cut, waste_area, 25.0)
        
        print(f"✓ Phase 2: Cutting operation successful (efficiency: {metrics.efficiency_score:.1f}%)")
        
        # Prepare data for Phase 3
        templates = [template]
        sheets = [sheet] + result.remainder_sheets
        cuts = [result.cut]
        project_metrics = metadata_tracker.get_project_metrics()
        
        # Phase 3a: Test project persistence
        project_handler = JSONProjectHandler()
        test_project_file = project_root / "integration_test_project.json"
        
        # Save project
        save_success = project_handler.save_project(
            str(test_project_file), templates, sheets, cuts, project_metrics,
            metadata={'integration_test': True, 'workflow': 'complete'}
        )
        
        if not save_success:
            print("✗ Project save failed")
            return False
        
        print("✓ Phase 3a: Project saved successfully")
        
        # Load project
        loaded_data = project_handler.load_project(str(test_project_file))
        
        if not loaded_data:
            print("✗ Project load failed")
            return False
        
        print("✓ Phase 3a: Project loaded successfully")
        
        # Verify loaded data
        if (len(loaded_data['templates']) != len(templates) or
            len(loaded_data['cuts']) != len(cuts)):
            print("✗ Loaded data doesn't match saved data")
            return False
        
        print("✓ Phase 3a: Data integrity verified")
        
        # Phase 3b: Test PDF export
        pdf_exporter = PDFExporter()
        test_pdf_file = project_root / "integration_test_report.pdf"
        
        export_success = pdf_exporter.export_project_report(
            str(test_pdf_file), templates, sheets, cuts, project_metrics
        )
        
        if not export_success:
            print("✗ PDF export failed")
            return False
        
        print("✓ Phase 3b: PDF report exported successfully")
        
        # Verify PDF file was created
        if not test_pdf_file.exists():
            print("✗ PDF file was not created")
            return False
        
        pdf_size = test_pdf_file.stat().st_size
        print(f"✓ Phase 3b: PDF file created ({pdf_size} bytes)")
        
        # Clean up test files
        if test_project_file.exists():
            test_project_file.unlink()
        if test_pdf_file.exists():
            test_pdf_file.unlink()
        
        print("✓ Integration test completed successfully")
        return True
        
    except Exception as e:
        print(f"✗ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_canvas_integration():
    """Test canvas integration (without GUI)."""
    print("Testing canvas integration...")
    
    try:
        import tkinter as tk
        from ui.canvas.infinite_canvas import InfiniteCanvas
        from ui.canvas.interactions import DragManager, get_snap_delta
        from shapely.geometry import box
        
        # Create hidden root window
        root = tk.Tk()
        root.withdraw()
        
        # Create canvas
        frame = tk.Frame(root)
        canvas = InfiniteCanvas(frame)
        
        # Test polygon drawing
        test_polygon = box(0, 0, 100, 50)
        item_id = canvas.draw_polygon(test_polygon, fill='blue')
        
        if item_id:
            print("✓ Canvas polygon drawing successful")
        else:
            print("✗ Canvas polygon drawing failed")
            return False
        
        # Test drag manager
        drag_manager = canvas.drag_manager
        if drag_manager:
            print("✓ Drag manager integration successful")
        else:
            print("✗ Drag manager integration failed")
            return False
        
        # Test snap function
        bbox1 = (0, 0, 100, 50)
        bbox2 = (105, 0, 205, 50)
        snap_delta = get_snap_delta(bbox1, bbox2, 10.0)
        
        if snap_delta is not None:
            print(f"✓ Snap function working: {snap_delta}")
        else:
            print("✓ Snap function working (no snap expected)")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Canvas integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_dxf_import_integration():
    """Test DXF import integration."""
    print("Testing DXF import integration...")
    
    try:
        from core.dxf_import.parser import DXFParser
        from core.dxf_import.converter import DXFConverter
        
        # Test parser creation
        parser = DXFParser()
        print("✓ DXF parser created")
        
        # Test converter creation
        converter = DXFConverter()
        print("✓ DXF converter created")
        
        # Note: We can't test actual DXF import without a DXF file,
        # but we can verify the components are properly integrated
        
        return True
        
    except Exception as e:
        print(f"✗ DXF import integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_window_integration():
    """Test main window integration (without showing GUI)."""
    print("Testing main window integration...")
    
    try:
        import tkinter as tk
        from ui.main_window import MainWindow
        
        # Create hidden root window
        root = tk.Tk()
        root.withdraw()
        
        # Create main window (but don't show it)
        main_window = MainWindow(root)
        
        # Test that all major components are initialized
        if not hasattr(main_window, 'cutting_engine'):
            print("✗ Cutting engine not initialized")
            return False
        
        if not hasattr(main_window, 'pdf_exporter'):
            print("✗ PDF exporter not initialized")
            return False
        
        if not hasattr(main_window, 'project_handler'):
            print("✗ Project handler not initialized")
            return False
        
        if not hasattr(main_window, 'canvas'):
            print("✗ Canvas not initialized")
            return False
        
        print("✓ Main window components initialized")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ Main window integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all integration tests."""
    print("=== Integration Tests ===\n")
    
    tests = [
        test_complete_workflow,
        test_canvas_integration,
        test_dxf_import_integration,
        test_main_window_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print(f"=== Integration Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All integration tests passed!")
        print("\nThe complete drywall optimization application is working correctly:")
        print("- ✅ Phase 1: Canvas with polygon rendering and interactions")
        print("- ✅ Phase 2: Cutting engine with metadata tracking")
        print("- ✅ Phase 3: Export and persistence functionality")
        print("- ✅ Component Integration: All phases work together seamlessly")
        return 0
    else:
        print("⚠️  Some integration tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
