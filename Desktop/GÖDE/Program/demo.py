#!/usr/bin/env python3
"""
Demo script for testing the InfiniteCanvas with polygon rendering and drag-and-drop.

This script demonstrates:
1. Creating an InfiniteCanvas in a tkinter window
2. Drawing Shapely polygons on the canvas
3. Drag-and-drop functionality with snapping
4. Zoom and pan operations
"""

import sys
import tkinter as tk
from tkinter import ttk
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Import our canvas
from ui.canvas.infinite_canvas import InfiniteCanvas
from shapely.geometry import box, Polygon
import math


class CanvasDemo:
    """Demo application for testing canvas functionality."""
    
    def __init__(self):
        """Initialize the demo application."""
        self.root = tk.Tk()
        self.root.title("Drywall Optimizer - Canvas Demo")
        self.root.geometry("1000x700")
        
        # Create UI
        self.create_widgets()
        self.create_test_objects()
        
        logger.info("Canvas demo initialized")
    
    def create_widgets(self):
        """Create the demo UI widgets."""
        # Main frame
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Controls")
        control_frame.pack(fill=tk.X, pady=(0, 5))
        
        # Buttons
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(button_frame, text="Add Rectangle", command=self.add_rectangle).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Add L-Shape", command=self.add_l_shape).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Clear All", command=self.clear_all).pack(side=tk.LEFT, padx=2)
        ttk.Button(button_frame, text="Zoom Fit", command=self.zoom_fit).pack(side=tk.LEFT, padx=2)
        
        # Snap controls
        snap_frame = ttk.Frame(control_frame)
        snap_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.snap_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(snap_frame, text="Enable Snapping", variable=self.snap_var, 
                       command=self.toggle_snap).pack(side=tk.LEFT)
        
        ttk.Label(snap_frame, text="Tolerance:").pack(side=tk.LEFT, padx=(10, 2))
        self.tolerance_var = tk.StringVar(value="5.0")
        tolerance_entry = ttk.Entry(snap_frame, textvariable=self.tolerance_var, width=8)
        tolerance_entry.pack(side=tk.LEFT, padx=2)
        tolerance_entry.bind('<Return>', self.update_tolerance)
        ttk.Label(snap_frame, text="mm").pack(side=tk.LEFT, padx=(2, 0))
        
        # Status frame
        status_frame = ttk.Frame(control_frame)
        status_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.status_label = ttk.Label(status_frame, text="Ready - Use mouse wheel to zoom, drag to pan or move objects")
        self.status_label.pack(side=tk.LEFT)
        
        self.zoom_label = ttk.Label(status_frame, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.RIGHT)
        
        # Canvas frame
        canvas_frame = ttk.LabelFrame(main_frame, text="Infinite Canvas")
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create the infinite canvas
        self.canvas = InfiniteCanvas(canvas_frame, self)
        
        # Object counter for unique colors
        self.object_count = 0
    
    def create_test_objects(self):
        """Create some test objects on the canvas."""
        # Test rectangle
        rect = box(0, 0, 200, 100)
        self.canvas.draw_polygon(rect, fill='lightblue', outline='blue', width=2)
        
        # Test square
        square = box(300, 50, 450, 200)
        self.canvas.draw_polygon(square, fill='lightgreen', outline='green', width=2)
        
        # Test L-shape
        l_shape = self.create_l_shape(100, 250, 150, 100, 50)
        self.canvas.draw_polygon(l_shape, fill='lightyellow', outline='orange', width=2)
        
        # Test circle approximation
        circle = self.create_circle(500, 300, 75)
        self.canvas.draw_polygon(circle, fill='lightcoral', outline='red', width=2)
        
        logger.info("Created test objects on canvas")
    
    def create_l_shape(self, x: float, y: float, width: float, height: float, notch_size: float) -> Polygon:
        """
        Create an L-shaped polygon.
        
        Args:
            x, y: Bottom-left corner
            width, height: Overall dimensions
            notch_size: Size of the notch
            
        Returns:
            Shapely Polygon representing the L-shape
        """
        coords = [
            (x, y),
            (x + width, y),
            (x + width, y + notch_size),
            (x + notch_size, y + notch_size),
            (x + notch_size, y + height),
            (x, y + height),
            (x, y)  # Close the polygon
        ]
        return Polygon(coords)
    
    def create_circle(self, center_x: float, center_y: float, radius: float, segments: int = 32) -> Polygon:
        """
        Create a circle approximation using a polygon.
        
        Args:
            center_x, center_y: Circle center
            radius: Circle radius
            segments: Number of segments for approximation
            
        Returns:
            Shapely Polygon approximating a circle
        """
        coords = []
        for i in range(segments):
            angle = 2 * math.pi * i / segments
            x = center_x + radius * math.cos(angle)
            y = center_y + radius * math.sin(angle)
            coords.append((x, y))
        
        return Polygon(coords)
    
    def add_rectangle(self):
        """Add a new rectangle to the canvas."""
        import random
        
        # Random position and size
        x = random.randint(-200, 400)
        y = random.randint(-100, 300)
        width = random.randint(50, 150)
        height = random.randint(30, 100)
        
        rect = box(x, y, x + width, y + height)
        
        # Random color
        colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightcoral', 'lightpink', 'lightgray']
        color = colors[self.object_count % len(colors)]
        
        self.canvas.draw_polygon(rect, fill=color, outline='black', width=1)
        self.object_count += 1
        
        self.update_status(f"Added rectangle at ({x}, {y})")
    
    def add_l_shape(self):
        """Add a new L-shape to the canvas."""
        import random
        
        # Random position and size
        x = random.randint(-200, 400)
        y = random.randint(-100, 300)
        width = random.randint(80, 120)
        height = random.randint(80, 120)
        notch = random.randint(30, 50)
        
        l_shape = self.create_l_shape(x, y, width, height, notch)
        
        # Random color
        colors = ['lightsteelblue', 'lightseagreen', 'lightsalmon', 'lightgoldenrodyellow']
        color = colors[self.object_count % len(colors)]
        
        self.canvas.draw_polygon(l_shape, fill=color, outline='darkblue', width=2)
        self.object_count += 1
        
        self.update_status(f"Added L-shape at ({x}, {y})")
    
    def clear_all(self):
        """Clear all objects from the canvas."""
        self.canvas.clear()
        self.object_count = 0
        self.update_status("Cleared all objects")
    
    def zoom_fit(self):
        """Fit all objects in view."""
        self.canvas.zoom_fit()
        self.update_status("Zoomed to fit all objects")
    
    def toggle_snap(self):
        """Toggle snapping on/off."""
        enabled = self.snap_var.get()
        tolerance = float(self.tolerance_var.get())
        self.canvas.drag_manager.set_snap_settings(enabled, tolerance)
        
        status = "enabled" if enabled else "disabled"
        self.update_status(f"Snapping {status}")
    
    def update_tolerance(self, event=None):
        """Update snap tolerance."""
        try:
            tolerance = float(self.tolerance_var.get())
            enabled = self.snap_var.get()
            self.canvas.drag_manager.set_snap_settings(enabled, tolerance)
            self.update_status(f"Snap tolerance set to {tolerance} mm")
        except ValueError:
            self.tolerance_var.set("5.0")
            self.update_status("Invalid tolerance value, reset to 5.0 mm")
    
    def update_status(self, message: str):
        """Update status message."""
        self.status_label.config(text=message)
        logger.info(f"Status: {message}")
    
    def run(self):
        """Run the demo application."""
        logger.info("Starting canvas demo")
        self.update_status("Demo ready - Try dragging objects, zooming with mouse wheel")
        self.root.mainloop()


def main():
    """Main function to run the demo."""
    try:
        demo = CanvasDemo()
        demo.run()
    except Exception as e:
        logger.error(f"Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
