# Core dependencies for Drywall Optimization Application

# DXF file processing
ezdxf>=1.1.0

# 2D geometry operations and polygon clipping
shapely>=2.0.0

# PDF generation for reports
reportlab>=4.0.0

# Enhanced GUI components (optional, for future improvements)
pillow>=10.0.0

# Spatial indexing for performance
rtree>=1.0.0

# Development and testing dependencies
pytest>=7.0.0
pytest-cov>=4.0.0

# Code quality
black>=23.0.0
flake8>=6.0.0

# Type checking
mypy>=1.0.0

# Documentation
sphinx>=7.0.0
