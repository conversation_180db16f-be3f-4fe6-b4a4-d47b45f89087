"""
Input validation utilities.
"""

import logging
from typing import Any, Optional, Union


logger = logging.getLogger(__name__)


def validate_environment() -> bool:
    """
    Validate that all required dependencies are available.
    
    Returns:
        True if environment is valid
    """
    required_modules = [
        'tkinter',
        'shapely', 
        'ezdxf',
        'reportlab'
    ]
    
    missing_modules = []
    
    for module_name in required_modules:
        try:
            __import__(module_name)
            logger.debug(f"Module {module_name} is available")
        except ImportError:
            missing_modules.append(module_name)
            logger.error(f"Required module {module_name} is not available")
    
    if missing_modules:
        logger.error(f"Missing required modules: {missing_modules}")
        return False
    
    # Test tkinter specifically (can fail even if importable)
    try:
        import tkinter as tk
        root = tk.Tk()
        root.withdraw()  # Hide the window
        root.destroy()
        logger.debug("Tkinter GUI test successful")
    except Exception as e:
        logger.error(f"Tkinter GUI test failed: {e}")
        return False
    
    logger.info("Environment validation successful")
    return True


def validate_numeric_input(value: Any, min_value: Optional[float] = None, 
                          max_value: Optional[float] = None, 
                          allow_zero: bool = True) -> tuple[bool, Optional[float]]:
    """
    Validate numeric input.
    
    Args:
        value: Input value to validate
        min_value: Minimum allowed value
        max_value: Maximum allowed value
        allow_zero: Whether zero is allowed
        
    Returns:
        Tuple of (is_valid, converted_value)
    """
    try:
        # Try to convert to float
        if isinstance(value, str):
            # Handle common decimal separators
            value = value.replace(',', '.')
            numeric_value = float(value)
        elif isinstance(value, (int, float)):
            numeric_value = float(value)
        else:
            return False, None
        
        # Check for NaN or infinity
        if not (numeric_value == numeric_value):  # NaN check
            return False, None
        if numeric_value == float('inf') or numeric_value == float('-inf'):
            return False, None
        
        # Check zero constraint
        if not allow_zero and numeric_value == 0:
            return False, None
        
        # Check range constraints
        if min_value is not None and numeric_value < min_value:
            return False, None
        if max_value is not None and numeric_value > max_value:
            return False, None
        
        return True, numeric_value
        
    except (ValueError, TypeError):
        return False, None


def validate_file_path(file_path: str, must_exist: bool = True, 
                      allowed_extensions: Optional[list] = None) -> bool:
    """
    Validate file path.
    
    Args:
        file_path: Path to validate
        must_exist: Whether file must exist
        allowed_extensions: List of allowed file extensions
        
    Returns:
        True if path is valid
    """
    try:
        from pathlib import Path
        
        path = Path(file_path)
        
        # Check if file exists (if required)
        if must_exist and not path.exists():
            return False
        
        # Check file extension
        if allowed_extensions:
            if path.suffix.lower() not in [ext.lower() for ext in allowed_extensions]:
                return False
        
        # Check if path is a file (not directory)
        if must_exist and path.exists() and not path.is_file():
            return False
        
        return True
        
    except Exception:
        return False


def validate_sheet_dimensions(width: Union[str, float], height: Union[str, float]) -> tuple[bool, Optional[float], Optional[float]]:
    """
    Validate sheet dimensions.
    
    Args:
        width: Width value
        height: Height value
        
    Returns:
        Tuple of (is_valid, validated_width, validated_height)
    """
    # Validate width
    width_valid, width_value = validate_numeric_input(
        width, min_value=1.0, max_value=10000.0, allow_zero=False
    )
    
    # Validate height  
    height_valid, height_value = validate_numeric_input(
        height, min_value=1.0, max_value=10000.0, allow_zero=False
    )
    
    if width_valid and height_valid:
        return True, width_value, height_value
    else:
        return False, None, None


def validate_snap_tolerance(tolerance: Union[str, float]) -> tuple[bool, Optional[float]]:
    """
    Validate snap tolerance value.
    
    Args:
        tolerance: Tolerance value to validate
        
    Returns:
        Tuple of (is_valid, validated_tolerance)
    """
    return validate_numeric_input(
        tolerance, min_value=0.1, max_value=100.0, allow_zero=False
    )


def validate_quantity(quantity: Union[str, int]) -> tuple[bool, Optional[int]]:
    """
    Validate quantity input.
    
    Args:
        quantity: Quantity value to validate
        
    Returns:
        Tuple of (is_valid, validated_quantity)
    """
    try:
        if isinstance(quantity, str):
            qty_value = int(quantity)
        elif isinstance(quantity, (int, float)):
            qty_value = int(quantity)
        else:
            return False, None
        
        if qty_value < 0 or qty_value > 10000:
            return False, None
        
        return True, qty_value
        
    except (ValueError, TypeError):
        return False, None


def sanitize_filename(filename: str) -> str:
    """
    Sanitize filename for safe file operations.
    
    Args:
        filename: Original filename
        
    Returns:
        Sanitized filename
    """
    import re
    
    # Remove or replace invalid characters
    sanitized = re.sub(r'[<>:"/\\|?*]', '_', filename)
    
    # Remove leading/trailing whitespace and dots
    sanitized = sanitized.strip(' .')
    
    # Ensure filename is not empty
    if not sanitized:
        sanitized = 'untitled'
    
    # Limit length
    if len(sanitized) > 200:
        sanitized = sanitized[:200]
    
    return sanitized
