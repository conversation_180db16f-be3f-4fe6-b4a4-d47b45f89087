"""
Unit conversion utilities.
"""

from typing import Dict, Union
import logging


logger = logging.getLogger(__name__)


class UnitConverter:
    """Utility class for unit conversions."""
    
    # Conversion factors to millimeters
    TO_MM = {
        'mm': 1.0,
        'cm': 10.0,
        'm': 1000.0,
        'inch': 25.4,
        'ft': 304.8
    }
    
    # Conversion factors from millimeters
    FROM_MM = {
        'mm': 1.0,
        'cm': 0.1,
        'm': 0.001,
        'inch': 1/25.4,
        'ft': 1/304.8
    }
    
    # Display names for units
    UNIT_NAMES = {
        'mm': 'Millimeters',
        'cm': 'Centimeters', 
        'm': 'Meters',
        'inch': 'Inches',
        'ft': 'Feet'
    }
    
    # Common abbreviations
    UNIT_SYMBOLS = {
        'mm': 'mm',
        'cm': 'cm',
        'm': 'm', 
        'inch': '"',
        'ft': "'"
    }
    
    @classmethod
    def convert(cls, value: float, from_unit: str, to_unit: str) -> float:
        """
        Convert value between units.
        
        Args:
            value: Value to convert
            from_unit: Source unit
            to_unit: Target unit
            
        Returns:
            Converted value
        """
        if from_unit == to_unit:
            return value
        
        if from_unit not in cls.TO_MM:
            raise ValueError(f"Unsupported source unit: {from_unit}")
        if to_unit not in cls.FROM_MM:
            raise ValueError(f"Unsupported target unit: {to_unit}")
        
        # Convert to mm first, then to target unit
        mm_value = value * cls.TO_MM[from_unit]
        result = mm_value * cls.FROM_MM[to_unit]
        
        return result
    
    @classmethod
    def to_mm(cls, value: float, from_unit: str) -> float:
        """
        Convert value to millimeters.
        
        Args:
            value: Value to convert
            from_unit: Source unit
            
        Returns:
            Value in millimeters
        """
        return cls.convert(value, from_unit, 'mm')
    
    @classmethod
    def from_mm(cls, value: float, to_unit: str) -> float:
        """
        Convert value from millimeters.
        
        Args:
            value: Value in millimeters
            to_unit: Target unit
            
        Returns:
            Converted value
        """
        return cls.convert(value, 'mm', to_unit)
    
    @classmethod
    def get_supported_units(cls) -> list:
        """Get list of supported units."""
        return list(cls.TO_MM.keys())
    
    @classmethod
    def get_unit_name(cls, unit: str) -> str:
        """Get display name for unit."""
        return cls.UNIT_NAMES.get(unit, unit)
    
    @classmethod
    def get_unit_symbol(cls, unit: str) -> str:
        """Get symbol for unit."""
        return cls.UNIT_SYMBOLS.get(unit, unit)
    
    @classmethod
    def format_value(cls, value: float, unit: str, precision: int = 1) -> str:
        """
        Format value with unit symbol.
        
        Args:
            value: Numeric value
            unit: Unit string
            precision: Decimal places
            
        Returns:
            Formatted string
        """
        symbol = cls.get_unit_symbol(unit)
        return f"{value:.{precision}f} {symbol}"
    
    @classmethod
    def parse_value_with_unit(cls, text: str) -> tuple[float, str]:
        """
        Parse value and unit from text.
        
        Args:
            text: Text containing value and unit
            
        Returns:
            Tuple of (value, unit)
        """
        import re
        
        # Remove whitespace
        text = text.strip()
        
        # Try to match number followed by unit
        pattern = r'^([\d.,]+)\s*([a-zA-Z"\']+)?$'
        match = re.match(pattern, text)
        
        if not match:
            raise ValueError(f"Cannot parse value and unit from: {text}")
        
        value_str = match.group(1).replace(',', '.')
        unit_str = match.group(2) or 'mm'  # Default to mm
        
        try:
            value = float(value_str)
        except ValueError:
            raise ValueError(f"Invalid numeric value: {value_str}")
        
        # Normalize unit
        unit = cls._normalize_unit(unit_str)
        
        return value, unit
    
    @classmethod
    def _normalize_unit(cls, unit_str: str) -> str:
        """Normalize unit string to standard form."""
        unit_str = unit_str.lower().strip()
        
        # Handle common variations
        unit_map = {
            'mm': 'mm',
            'millimeter': 'mm',
            'millimeters': 'mm',
            'cm': 'cm', 
            'centimeter': 'cm',
            'centimeters': 'cm',
            'm': 'm',
            'meter': 'm',
            'meters': 'm',
            'metre': 'm',
            'metres': 'm',
            'in': 'inch',
            'inch': 'inch',
            'inches': 'inch',
            '"': 'inch',
            'ft': 'ft',
            'foot': 'ft',
            'feet': 'ft',
            "'": 'ft'
        }
        
        normalized = unit_map.get(unit_str)
        if normalized is None:
            raise ValueError(f"Unknown unit: {unit_str}")
        
        return normalized
    
    @classmethod
    def get_scale_factor(cls, from_unit: str, to_unit: str) -> float:
        """
        Get scale factor for converting between units.
        
        Args:
            from_unit: Source unit
            to_unit: Target unit
            
        Returns:
            Scale factor (multiply source by this to get target)
        """
        return cls.convert(1.0, from_unit, to_unit)
    
    @classmethod
    def convert_coordinates(cls, coords: list, from_unit: str, to_unit: str) -> list:
        """
        Convert list of coordinate tuples between units.
        
        Args:
            coords: List of (x, y) coordinate tuples
            from_unit: Source unit
            to_unit: Target unit
            
        Returns:
            List of converted coordinates
        """
        if from_unit == to_unit:
            return coords
        
        scale_factor = cls.get_scale_factor(from_unit, to_unit)
        
        return [(x * scale_factor, y * scale_factor) for x, y in coords]
