"""
Project state serialization for save/load functionality.
"""

import logging
import json
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from core.geometry.shapes import Template, Sheet, Cut, Point, LineSegment
from core.cutting.metadata import ProjectMetrics, CutMetrics
from shapely.geometry import Polygon
from shapely import wkt

logger = logging.getLogger(__name__)


class ProjectSerializer:
    """
    Handles serialization and deserialization of project state.
    
    Features:
    - Complete project state serialization to JSON
    - Geometry serialization using WKT format
    - Metadata preservation
    - Version compatibility
    - Error recovery
    """
    
    VERSION = "1.0"
    
    def __init__(self):
        """Initialize project serializer."""
        logger.info("ProjectSerializer initialized")
    
    def serialize_project(self, templates: List[Template], sheets: List[Sheet],
                         cuts: List[Cut], project_metrics: ProjectMetrics,
                         metadata: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        Serialize complete project state to dictionary.
        
        Args:
            templates: List of templates in project
            sheets: List of sheets (including remainders)
            cuts: List of cut operations
            project_metrics: Project performance metrics
            metadata: Additional project metadata
            
        Returns:
            Dictionary containing serialized project state
        """
        try:
            project_data = {
                'version': self.VERSION,
                'timestamp': datetime.now().isoformat(),
                'metadata': metadata or {},
                'templates': [self._serialize_template(t) for t in templates],
                'sheets': [self._serialize_sheet(s) for s in sheets],
                'cuts': [self._serialize_cut(c) for c in cuts],
                'project_metrics': self._serialize_project_metrics(project_metrics)
            }
            
            logger.info(f"Serialized project with {len(templates)} templates, {len(sheets)} sheets, {len(cuts)} cuts")
            return project_data
            
        except Exception as e:
            logger.error(f"Failed to serialize project: {e}")
            raise
    
    def deserialize_project(self, project_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Deserialize project state from dictionary.
        
        Args:
            project_data: Dictionary containing serialized project state
            
        Returns:
            Dictionary with deserialized objects
        """
        try:
            # Check version compatibility
            version = project_data.get('version', '1.0')
            if not self._is_version_compatible(version):
                logger.warning(f"Project version {version} may not be fully compatible")
            
            # Deserialize components
            templates = [self._deserialize_template(t) for t in project_data.get('templates', [])]
            sheets = [self._deserialize_sheet(s) for s in project_data.get('sheets', [])]
            cuts = [self._deserialize_cut(c) for c in project_data.get('cuts', [])]
            
            # Deserialize metrics if available
            project_metrics = None
            if 'project_metrics' in project_data:
                project_metrics = self._deserialize_project_metrics(project_data['project_metrics'])
            
            result = {
                'templates': templates,
                'sheets': sheets,
                'cuts': cuts,
                'project_metrics': project_metrics,
                'metadata': project_data.get('metadata', {}),
                'version': version,
                'timestamp': project_data.get('timestamp')
            }
            
            logger.info(f"Deserialized project with {len(templates)} templates, {len(sheets)} sheets, {len(cuts)} cuts")
            return result
            
        except Exception as e:
            logger.error(f"Failed to deserialize project: {e}")
            raise
    
    def _serialize_template(self, template: Template) -> Dict[str, Any]:
        """Serialize a template object."""
        return {
            'id': template.id,
            'polygon_wkt': template.polygon.wkt,
            'position': self._serialize_point(template.position),
            'rotation': template.rotation,
            'metadata': template.metadata
        }
    
    def _deserialize_template(self, data: Dict[str, Any]) -> Template:
        """Deserialize a template object."""
        polygon = wkt.loads(data['polygon_wkt'])
        position = self._deserialize_point(data['position'])
        
        return Template(
            id=data['id'],
            polygon=polygon,
            position=position,
            rotation=data.get('rotation', 0.0),
            metadata=data.get('metadata', {})
        )
    
    def _serialize_sheet(self, sheet: Sheet) -> Dict[str, Any]:
        """Serialize a sheet object."""
        return {
            'id': sheet.id,
            'width': sheet.width,
            'height': sheet.height,
            'polygon_wkt': sheet.polygon.wkt,
            'position': self._serialize_point(sheet.position),
            'rotation': sheet.rotation,
            'is_remainder': sheet.is_remainder,
            'parent_sheet_id': sheet.parent_sheet_id,
            'metadata': sheet.metadata
        }
    
    def _deserialize_sheet(self, data: Dict[str, Any]) -> Sheet:
        """Deserialize a sheet object."""
        polygon = wkt.loads(data['polygon_wkt'])
        position = self._deserialize_point(data['position'])
        
        return Sheet(
            id=data['id'],
            width=data['width'],
            height=data['height'],
            polygon=polygon,
            position=position,
            rotation=data.get('rotation', 0.0),
            is_remainder=data.get('is_remainder', False),
            parent_sheet_id=data.get('parent_sheet_id'),
            metadata=data.get('metadata', {})
        )
    
    def _serialize_cut(self, cut: Cut) -> Dict[str, Any]:
        """Serialize a cut object."""
        return {
            'id': cut.id,
            'template_id': cut.template_id,
            'sheet_id': cut.sheet_id,
            'cut_polygon_wkt': cut.cut_polygon.wkt,
            'cut_lines': [self._serialize_line_segment(line) for line in cut.cut_lines],
            'timestamp': cut.timestamp,
            'metadata': cut.metadata
        }
    
    def _deserialize_cut(self, data: Dict[str, Any]) -> Cut:
        """Deserialize a cut object."""
        cut_polygon = wkt.loads(data['cut_polygon_wkt'])
        cut_lines = [self._deserialize_line_segment(line) for line in data.get('cut_lines', [])]
        
        return Cut(
            id=data['id'],
            template_id=data['template_id'],
            sheet_id=data['sheet_id'],
            cut_polygon=cut_polygon,
            cut_lines=cut_lines,
            timestamp=data.get('timestamp'),
            metadata=data.get('metadata', {})
        )
    
    def _serialize_point(self, point: Point) -> Dict[str, float]:
        """Serialize a point object."""
        return {
            'x': point.x,
            'y': point.y
        }
    
    def _deserialize_point(self, data: Dict[str, float]) -> Point:
        """Deserialize a point object."""
        return Point(data['x'], data['y'])
    
    def _serialize_line_segment(self, line: LineSegment) -> Dict[str, Any]:
        """Serialize a line segment object."""
        return {
            'start': self._serialize_point(line.start),
            'end': self._serialize_point(line.end)
        }
    
    def _deserialize_line_segment(self, data: Dict[str, Any]) -> LineSegment:
        """Deserialize a line segment object."""
        start = self._deserialize_point(data['start'])
        end = self._deserialize_point(data['end'])
        return LineSegment(start, end)
    
    def _serialize_project_metrics(self, metrics: ProjectMetrics) -> Dict[str, Any]:
        """Serialize project metrics."""
        return {
            'total_cuts': metrics.total_cuts,
            'total_cut_area_mm2': metrics.total_cut_area_mm2,
            'total_waste_area_mm2': metrics.total_waste_area_mm2,
            'total_cut_length_mm': metrics.total_cut_length_mm,
            'average_utilization': metrics.average_utilization,
            'average_efficiency': metrics.average_efficiency,
            'total_processing_time_ms': metrics.total_processing_time_ms,
            'sheets_used': metrics.sheets_used,
            'templates_cut': metrics.templates_cut
        }
    
    def _deserialize_project_metrics(self, data: Dict[str, Any]) -> ProjectMetrics:
        """Deserialize project metrics."""
        return ProjectMetrics(
            total_cuts=data.get('total_cuts', 0),
            total_cut_area_mm2=data.get('total_cut_area_mm2', 0.0),
            total_waste_area_mm2=data.get('total_waste_area_mm2', 0.0),
            total_cut_length_mm=data.get('total_cut_length_mm', 0.0),
            average_utilization=data.get('average_utilization', 0.0),
            average_efficiency=data.get('average_efficiency', 0.0),
            total_processing_time_ms=data.get('total_processing_time_ms', 0.0),
            sheets_used=data.get('sheets_used', 0),
            templates_cut=data.get('templates_cut', 0)
        )
    
    def _is_version_compatible(self, version: str) -> bool:
        """Check if project version is compatible."""
        try:
            major, minor = version.split('.')[:2]
            current_major, current_minor = self.VERSION.split('.')[:2]
            
            # Same major version is compatible
            return major == current_major
            
        except (ValueError, IndexError):
            logger.warning(f"Invalid version format: {version}")
            return False
    
    def validate_project_data(self, project_data: Dict[str, Any]) -> bool:
        """
        Validate project data structure.
        
        Args:
            project_data: Project data to validate
            
        Returns:
            True if data is valid
        """
        try:
            # Check required fields
            required_fields = ['version', 'templates', 'sheets', 'cuts']
            for field in required_fields:
                if field not in project_data:
                    logger.error(f"Missing required field: {field}")
                    return False
            
            # Check data types
            if not isinstance(project_data['templates'], list):
                logger.error("Templates must be a list")
                return False
            
            if not isinstance(project_data['sheets'], list):
                logger.error("Sheets must be a list")
                return False
            
            if not isinstance(project_data['cuts'], list):
                logger.error("Cuts must be a list")
                return False
            
            # Validate version
            if not self._is_version_compatible(project_data['version']):
                logger.warning(f"Version {project_data['version']} may not be compatible")
            
            logger.info("Project data validation successful")
            return True
            
        except Exception as e:
            logger.error(f"Project data validation failed: {e}")
            return False
