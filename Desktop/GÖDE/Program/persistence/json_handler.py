"""
JSON file handling for project persistence.
"""

import logging
import json
import shutil
from typing import Dict, List, Any, Optional
from datetime import datetime
from pathlib import Path

from .project_serializer import ProjectSerializer
from core.geometry.shapes import Template, Sheet, Cut
from core.cutting.metadata import ProjectMetrics

logger = logging.getLogger(__name__)


class JSONProjectHandler:
    """
    Handles JSON file operations for project persistence.
    
    Features:
    - Save/load projects to/from JSON files
    - Automatic backup creation
    - Error recovery
    - File validation
    - Compression support
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize JSON project handler.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        self.serializer = ProjectSerializer()
        
        # Configuration
        self.create_backups = self.config.get('create_backups', True)
        self.max_backups = self.config.get('max_backups', 5)
        self.compress_files = self.config.get('compress_files', False)
        
        logger.info("JSONProjectHandler initialized")
    
    def save_project(self, file_path: str, templates: List[Template], 
                    sheets: List[Sheet], cuts: List[Cut],
                    project_metrics: ProjectMetrics,
                    metadata: Optional[Dict[str, Any]] = None) -> bool:
        """
        Save project to JSON file.
        
        Args:
            file_path: Path to save project file
            templates: List of templates
            sheets: List of sheets
            cuts: List of cuts
            project_metrics: Project metrics
            metadata: Additional project metadata
            
        Returns:
            True if save successful
        """
        try:
            logger.info(f"Saving project to {file_path}")
            
            # Create backup if file exists
            if self.create_backups and Path(file_path).exists():
                self._create_backup(file_path)
            
            # Serialize project data
            project_data = self.serializer.serialize_project(
                templates, sheets, cuts, project_metrics, metadata
            )
            
            # Add file metadata
            project_data['file_metadata'] = {
                'saved_timestamp': datetime.now().isoformat(),
                'file_version': '1.0',
                'application': 'Drywall Optimizer',
                'file_size_estimate': 0  # Will be updated after save
            }
            
            # Save to file
            file_path_obj = Path(file_path)
            file_path_obj.parent.mkdir(parents=True, exist_ok=True)
            
            if self.compress_files:
                self._save_compressed(file_path, project_data)
            else:
                self._save_json(file_path, project_data)
            
            # Update file size in metadata
            file_size = file_path_obj.stat().st_size
            project_data['file_metadata']['file_size_estimate'] = file_size
            
            logger.info(f"Successfully saved project to {file_path} ({file_size} bytes)")
            return True
            
        except Exception as e:
            logger.error(f"Failed to save project to {file_path}: {e}")
            return False
    
    def load_project(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Load project from JSON file.
        
        Args:
            file_path: Path to project file
            
        Returns:
            Dictionary with project data or None if failed
        """
        try:
            logger.info(f"Loading project from {file_path}")
            
            # Check if file exists
            if not Path(file_path).exists():
                logger.error(f"Project file not found: {file_path}")
                return None
            
            # Load project data
            if self.compress_files and file_path.endswith('.gz'):
                project_data = self._load_compressed(file_path)
            else:
                project_data = self._load_json(file_path)
            
            # Validate project data
            if not self.serializer.validate_project_data(project_data):
                logger.error("Project data validation failed")
                return None
            
            # Deserialize project
            result = self.serializer.deserialize_project(project_data)
            
            # Add file information
            result['file_path'] = file_path
            result['file_metadata'] = project_data.get('file_metadata', {})
            
            logger.info(f"Successfully loaded project from {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to load project from {file_path}: {e}")
            
            # Try to recover from backup
            if self.create_backups:
                return self._try_recover_from_backup(file_path)
            
            return None
    
    def _save_json(self, file_path: str, data: Dict[str, Any]):
        """Save data as JSON file."""
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False, default=str)
    
    def _load_json(self, file_path: str) -> Dict[str, Any]:
        """Load data from JSON file."""
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def _save_compressed(self, file_path: str, data: Dict[str, Any]):
        """Save data as compressed JSON file."""
        import gzip
        
        json_str = json.dumps(data, indent=2, ensure_ascii=False, default=str)
        
        with gzip.open(file_path + '.gz', 'wt', encoding='utf-8') as f:
            f.write(json_str)
    
    def _load_compressed(self, file_path: str) -> Dict[str, Any]:
        """Load data from compressed JSON file."""
        import gzip
        
        with gzip.open(file_path, 'rt', encoding='utf-8') as f:
            return json.load(f)
    
    def _create_backup(self, file_path: str):
        """Create backup of existing file."""
        try:
            file_path_obj = Path(file_path)
            
            if not file_path_obj.exists():
                return
            
            # Create backup directory
            backup_dir = file_path_obj.parent / 'backups'
            backup_dir.mkdir(exist_ok=True)
            
            # Generate backup filename with timestamp
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_name = f"{file_path_obj.stem}_{timestamp}{file_path_obj.suffix}"
            backup_path = backup_dir / backup_name
            
            # Copy file to backup
            shutil.copy2(file_path, backup_path)
            
            # Clean up old backups
            self._cleanup_old_backups(backup_dir, file_path_obj.stem)
            
            logger.info(f"Created backup: {backup_path}")
            
        except Exception as e:
            logger.warning(f"Failed to create backup: {e}")
    
    def _cleanup_old_backups(self, backup_dir: Path, file_stem: str):
        """Clean up old backup files."""
        try:
            # Find all backup files for this project
            backup_pattern = f"{file_stem}_*.json"
            backup_files = list(backup_dir.glob(backup_pattern))
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Remove excess backups
            if len(backup_files) > self.max_backups:
                for old_backup in backup_files[self.max_backups:]:
                    old_backup.unlink()
                    logger.debug(f"Removed old backup: {old_backup}")
            
        except Exception as e:
            logger.warning(f"Failed to cleanup old backups: {e}")
    
    def _try_recover_from_backup(self, file_path: str) -> Optional[Dict[str, Any]]:
        """Try to recover project from backup files."""
        try:
            file_path_obj = Path(file_path)
            backup_dir = file_path_obj.parent / 'backups'
            
            if not backup_dir.exists():
                return None
            
            # Find backup files
            backup_pattern = f"{file_path_obj.stem}_*.json"
            backup_files = list(backup_dir.glob(backup_pattern))
            
            if not backup_files:
                return None
            
            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
            
            # Try to load from most recent backup
            for backup_file in backup_files:
                try:
                    logger.info(f"Attempting recovery from backup: {backup_file}")
                    
                    project_data = self._load_json(str(backup_file))
                    
                    if self.serializer.validate_project_data(project_data):
                        result = self.serializer.deserialize_project(project_data)
                        result['file_path'] = file_path
                        result['recovered_from_backup'] = str(backup_file)
                        
                        logger.info(f"Successfully recovered from backup: {backup_file}")
                        return result
                    
                except Exception as e:
                    logger.warning(f"Failed to recover from backup {backup_file}: {e}")
                    continue
            
            logger.error("All backup recovery attempts failed")
            return None
            
        except Exception as e:
            logger.error(f"Backup recovery failed: {e}")
            return None
    
    def list_backups(self, file_path: str) -> List[Dict[str, Any]]:
        """
        List available backup files for a project.
        
        Args:
            file_path: Original project file path
            
        Returns:
            List of backup file information
        """
        try:
            file_path_obj = Path(file_path)
            backup_dir = file_path_obj.parent / 'backups'
            
            if not backup_dir.exists():
                return []
            
            backup_pattern = f"{file_path_obj.stem}_*.json"
            backup_files = list(backup_dir.glob(backup_pattern))
            
            backups = []
            for backup_file in backup_files:
                stat = backup_file.stat()
                backups.append({
                    'path': str(backup_file),
                    'name': backup_file.name,
                    'size': stat.st_size,
                    'modified': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                    'created': datetime.fromtimestamp(stat.st_ctime).isoformat()
                })
            
            # Sort by modification time (newest first)
            backups.sort(key=lambda x: x['modified'], reverse=True)
            
            return backups
            
        except Exception as e:
            logger.error(f"Failed to list backups: {e}")
            return []
    
    def export_project_summary(self, file_path: str) -> Optional[Dict[str, Any]]:
        """
        Export project summary without full geometry data.
        
        Args:
            file_path: Path to project file
            
        Returns:
            Project summary dictionary
        """
        try:
            project_data = self.load_project(file_path)
            
            if not project_data:
                return None
            
            # Create summary with minimal data
            summary = {
                'file_path': file_path,
                'version': project_data.get('version'),
                'timestamp': project_data.get('timestamp'),
                'metadata': project_data.get('metadata', {}),
                'statistics': {
                    'templates_count': len(project_data.get('templates', [])),
                    'sheets_count': len(project_data.get('sheets', [])),
                    'cuts_count': len(project_data.get('cuts', [])),
                    'remainder_sheets': len([s for s in project_data.get('sheets', []) if s.is_remainder])
                }
            }
            
            # Add project metrics if available
            if project_data.get('project_metrics'):
                metrics = project_data['project_metrics']
                summary['performance'] = {
                    'total_cut_area': metrics.total_cut_area_mm2,
                    'total_waste_area': metrics.total_waste_area_mm2,
                    'material_efficiency': metrics.material_efficiency,
                    'average_efficiency': metrics.average_efficiency
                }
            
            return summary
            
        except Exception as e:
            logger.error(f"Failed to export project summary: {e}")
            return None
