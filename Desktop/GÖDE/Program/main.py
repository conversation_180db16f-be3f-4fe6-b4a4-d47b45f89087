#!/usr/bin/env python3
"""
Drywall Optimization Application
Main entry point for the desktop application.
"""

import sys
import tkinter as tk
from tkinter import messagebox
import logging
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow
from utils.validation import validate_environment

def setup_logging():
    """Configure logging for the application."""
    log_dir = project_root / "logs"
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "drywall_optimizer.log"),
            logging.StreamHandler(sys.stdout)
        ]
    )

def main():
    """Main application entry point."""
    try:
        # Setup logging
        setup_logging()
        logger = logging.getLogger(__name__)
        logger.info("Starting Drywall Optimization Application")
        
        # Validate environment
        if not validate_environment():
            messagebox.showerror(
                "Environment Error", 
                "Required dependencies are missing. Please check the installation."
            )
            return 1
        
        # Create and run the main application
        root = tk.Tk()
        app = MainWindow(root)
        
        # Configure window close behavior
        def on_closing():
            if app.confirm_exit():
                root.destroy()
        
        root.protocol("WM_DELETE_WINDOW", on_closing)
        
        # Start the GUI event loop
        logger.info("Application started successfully")
        root.mainloop()
        
        logger.info("Application closed")
        return 0
        
    except Exception as e:
        error_msg = f"Fatal error: {str(e)}"
        logging.error(error_msg, exc_info=True)
        
        # Show error dialog if possible
        try:
            messagebox.showerror("Fatal Error", error_msg)
        except:
            print(error_msg, file=sys.stderr)
        
        return 1

if __name__ == "__main__":
    sys.exit(main())
