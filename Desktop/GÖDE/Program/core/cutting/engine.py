"""
Main cutting engine for drywall optimization.

This module implements the core cutting logic including:
- Template-to-sheet cutting operations
- Remainder piece management
- Cut validation and optimization
- Metadata tracking for all cuts
"""

import logging
import uuid
from typing import List, Tuple, Optional, Dict, Any
from datetime import datetime
from shapely.geometry import Polygon
from shapely.validation import make_valid

from ..geometry.shapes import Template, Sheet, Cut, Point, LineSegment
from ..geometry.operations import GeometryOperations

logger = logging.getLogger(__name__)


class CuttingEngine:
    """
    Main engine for performing cutting operations on drywall sheets.
    
    Features:
    - Template-to-sheet cutting with remainder management
    - Cut validation and quality checks
    - Metadata tracking for all operations
    - Support for complex polygon shapes (L-shapes, U-shapes, etc.)
    - Automatic cleanup of small remainder pieces
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize the cutting engine.
        
        Args:
            config: Configuration dictionary with cutting settings
        """
        self.config = config or {}
        
        # Cutting settings from config
        self.precision_mm = self.config.get('precision_mm', 0.1)
        self.auto_cleanup_small_pieces = self.config.get('auto_cleanup_small_pieces', True)
        self.min_piece_area_mm2 = self.config.get('min_piece_area_mm2', 10000)  # 100cm²
        
        # Cut history for undo functionality
        self.cut_history: List[CutOperation] = []
        self.max_history_size = 50
        
        logger.info(f"CuttingEngine initialized with precision={self.precision_mm}mm")
    
    def cut_template_from_sheet(self, template: Template, sheet: Sheet, 
                               validate_overlap: bool = True) -> 'CutResult':
        """
        Cut a template from a sheet, creating a cut piece and remainder pieces.
        
        Args:
            template: Template to cut out
            sheet: Sheet to cut from
            validate_overlap: Whether to validate that template overlaps with sheet
            
        Returns:
            CutResult containing cut piece, remainders, and metadata
        """
        logger.info(f"Cutting template {template.id} from sheet {sheet.id}")
        
        try:
            # Validate inputs
            if not self._validate_cut_inputs(template, sheet, validate_overlap):
                return CutResult.create_failed("Invalid cut inputs")
            
            # Perform the geometric cutting operation
            cut_polygon, remainder_polygons = GeometryOperations.clip_polygon(
                sheet.polygon, template.polygon
            )
            
            if cut_polygon is None:
                return CutResult.create_failed("No intersection between template and sheet")
            
            # Calculate cut lines for metadata
            cut_lines = GeometryOperations.calculate_cut_lines(
                template.polygon, sheet.polygon
            )
            
            # Create cut metadata
            cut = Cut(
                template_id=template.id,
                sheet_id=sheet.id,
                cut_polygon=cut_polygon,
                cut_lines=cut_lines,
                metadata={
                    'cut_area_mm2': cut_polygon.area,
                    'total_cut_length_mm': sum(line.length for line in cut_lines),
                    'sheet_utilization': cut_polygon.area / sheet.polygon.area,
                    'template_coverage': cut_polygon.area / template.polygon.area
                }
            )
            
            # Create remainder sheets
            remainder_sheets = self._create_remainder_sheets(
                remainder_polygons, sheet, cut.id
            )
            
            # Clean up small pieces if enabled
            if self.auto_cleanup_small_pieces:
                remainder_sheets = self._cleanup_small_pieces(remainder_sheets)
            
            # Create cut operation for history
            cut_operation = CutOperation(
                cut=cut,
                original_sheet=sheet,
                remainder_sheets=remainder_sheets,
                template=template
            )
            
            # Add to history
            self._add_to_history(cut_operation)
            
            # Create result
            result = CutResult(
                success=True,
                cut=cut,
                remainder_sheets=remainder_sheets,
                original_sheet=sheet,
                template=template,
                message=f"Successfully cut template {template.id} from sheet {sheet.id}"
            )
            
            logger.info(f"Cut completed: {len(remainder_sheets)} remainder pieces created")
            return result
            
        except Exception as e:
            logger.error(f"Cutting operation failed: {e}")
            return CutResult.create_failed(f"Cutting failed: {str(e)}")
    
    def _validate_cut_inputs(self, template: Template, sheet: Sheet, 
                           validate_overlap: bool) -> bool:
        """
        Validate that the cutting inputs are valid.
        
        Args:
            template: Template to validate
            sheet: Sheet to validate
            validate_overlap: Whether to check for overlap
            
        Returns:
            True if inputs are valid
        """
        # Check that polygons are valid
        if not template.polygon.is_valid:
            logger.warning(f"Template {template.id} has invalid polygon")
            return False
        
        if not sheet.polygon.is_valid:
            logger.warning(f"Sheet {sheet.id} has invalid polygon")
            return False
        
        # Check for overlap if required
        if validate_overlap:
            if not GeometryOperations.polygons_intersect(template.polygon, sheet.polygon):
                logger.warning(f"Template {template.id} does not overlap with sheet {sheet.id}")
                return False
        
        # Check minimum areas
        if template.polygon.area < 1.0:  # 1 mm²
            logger.warning(f"Template {template.id} area too small: {template.polygon.area}")
            return False
        
        if sheet.polygon.area < 1.0:  # 1 mm²
            logger.warning(f"Sheet {sheet.id} area too small: {sheet.polygon.area}")
            return False
        
        return True
    
    def _create_remainder_sheets(self, remainder_polygons: List[Polygon], 
                               original_sheet: Sheet, cut_id: str) -> List[Sheet]:
        """
        Create Sheet objects from remainder polygons.
        
        Args:
            remainder_polygons: List of remainder polygon geometries
            original_sheet: Original sheet that was cut
            cut_id: ID of the cut operation
            
        Returns:
            List of remainder Sheet objects
        """
        remainder_sheets = []
        
        for i, polygon in enumerate(remainder_polygons):
            # Validate and fix polygon if needed
            if not polygon.is_valid:
                polygon = make_valid(polygon)
                if not polygon.is_valid or polygon.is_empty:
                    logger.warning(f"Could not fix invalid remainder polygon {i}")
                    continue
            
            # Skip very small pieces
            if polygon.area < self.min_piece_area_mm2:
                logger.debug(f"Skipping small remainder piece {i} with area {polygon.area}")
                continue
            
            # Create remainder sheet
            remainder = Sheet(
                polygon=polygon,
                is_remainder=True,
                parent_sheet_id=original_sheet.id,
                metadata={
                    'cut_id': cut_id,
                    'remainder_index': i,
                    'original_sheet_id': original_sheet.id,
                    'area_mm2': polygon.area,
                    'created_timestamp': datetime.now().isoformat()
                }
            )
            
            remainder_sheets.append(remainder)
        
        return remainder_sheets
    
    def _cleanup_small_pieces(self, sheets: List[Sheet]) -> List[Sheet]:
        """
        Remove sheets that are too small to be useful.
        
        Args:
            sheets: List of sheets to filter
            
        Returns:
            Filtered list of sheets
        """
        cleaned_sheets = []
        removed_count = 0
        
        for sheet in sheets:
            if sheet.polygon.area >= self.min_piece_area_mm2:
                cleaned_sheets.append(sheet)
            else:
                removed_count += 1
                logger.debug(f"Removed small piece with area {sheet.polygon.area} mm²")
        
        if removed_count > 0:
            logger.info(f"Cleaned up {removed_count} small pieces")
        
        return cleaned_sheets
    
    def _add_to_history(self, cut_operation: 'CutOperation'):
        """Add cut operation to history for undo functionality."""
        self.cut_history.append(cut_operation)
        
        # Limit history size
        if len(self.cut_history) > self.max_history_size:
            self.cut_history.pop(0)
    
    def can_undo(self) -> bool:
        """Check if there are operations that can be undone."""
        return len(self.cut_history) > 0
    
    def undo_last_cut(self) -> Optional['CutOperation']:
        """
        Undo the last cutting operation.
        
        Returns:
            The undone CutOperation, or None if no operations to undo
        """
        if not self.can_undo():
            return None
        
        last_operation = self.cut_history.pop()
        logger.info(f"Undoing cut operation: {last_operation.cut.id}")
        
        return last_operation
    
    def get_cut_statistics(self) -> Dict[str, Any]:
        """
        Get statistics about cutting operations.
        
        Returns:
            Dictionary with cutting statistics
        """
        if not self.cut_history:
            return {
                'total_cuts': 0,
                'total_cut_length_mm': 0,
                'total_waste_area_mm2': 0,
                'average_utilization': 0
            }
        
        total_cuts = len(self.cut_history)
        total_cut_length = sum(op.cut.total_cut_length for op in self.cut_history)
        total_waste_area = sum(
            sum(sheet.polygon.area for sheet in op.remainder_sheets)
            for op in self.cut_history
        )
        
        utilizations = [
            op.cut.metadata.get('sheet_utilization', 0)
            for op in self.cut_history
        ]
        average_utilization = sum(utilizations) / len(utilizations) if utilizations else 0
        
        return {
            'total_cuts': total_cuts,
            'total_cut_length_mm': total_cut_length,
            'total_waste_area_mm2': total_waste_area,
            'average_utilization': average_utilization,
            'history_size': len(self.cut_history)
        }


class CutResult:
    """
    Result of a cutting operation.

    Contains the cut piece, remainder pieces, and operation metadata.
    """

    def __init__(self, success: bool, cut: Optional[Cut] = None,
                 remainder_sheets: Optional[List[Sheet]] = None,
                 original_sheet: Optional[Sheet] = None,
                 template: Optional[Template] = None,
                 message: str = ""):
        """
        Initialize cut result.

        Args:
            success: Whether the cut was successful
            cut: The cut operation metadata
            remainder_sheets: List of remainder sheet pieces
            original_sheet: The original sheet that was cut
            template: The template that was cut out
            message: Result message
        """
        self.success = success
        self.cut = cut
        self.remainder_sheets = remainder_sheets or []
        self.original_sheet = original_sheet
        self.template = template
        self.message = message
        self.timestamp = datetime.now().isoformat()

    @classmethod
    def create_failed(cls, message: str) -> 'CutResult':
        """Create a failed cut result."""
        return cls(success=False, message=message)

    @property
    def cut_area(self) -> float:
        """Get the area of the cut piece."""
        return self.cut.cut_area if self.cut else 0.0

    @property
    def waste_area(self) -> float:
        """Get the total area of waste (remainder pieces)."""
        return sum(sheet.polygon.area for sheet in self.remainder_sheets)

    @property
    def utilization_ratio(self) -> float:
        """Get the utilization ratio (cut area / original sheet area)."""
        if not self.original_sheet or not self.cut:
            return 0.0
        return self.cut_area / self.original_sheet.polygon.area

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'success': self.success,
            'message': self.message,
            'timestamp': self.timestamp,
            'cut': self.cut.to_dict() if self.cut else None,
            'remainder_sheets': [sheet.to_dict() for sheet in self.remainder_sheets],
            'original_sheet': self.original_sheet.to_dict() if self.original_sheet else None,
            'template': self.template.to_dict() if self.template else None,
            'cut_area': self.cut_area,
            'waste_area': self.waste_area,
            'utilization_ratio': self.utilization_ratio
        }


class CutOperation:
    """
    Represents a complete cutting operation for undo functionality.

    Stores all information needed to reverse a cutting operation.
    """

    def __init__(self, cut: Cut, original_sheet: Sheet,
                 remainder_sheets: List[Sheet], template: Template):
        """
        Initialize cut operation.

        Args:
            cut: The cut metadata
            original_sheet: Original sheet before cutting
            remainder_sheets: Resulting remainder pieces
            template: Template that was cut out
        """
        self.cut = cut
        self.original_sheet = original_sheet
        self.remainder_sheets = remainder_sheets
        self.template = template
        self.timestamp = datetime.now().isoformat()
        self.operation_id = str(uuid.uuid4())

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            'operation_id': self.operation_id,
            'timestamp': self.timestamp,
            'cut': self.cut.to_dict(),
            'original_sheet': self.original_sheet.to_dict(),
            'remainder_sheets': [sheet.to_dict() for sheet in self.remainder_sheets],
            'template': self.template.to_dict()
        }
