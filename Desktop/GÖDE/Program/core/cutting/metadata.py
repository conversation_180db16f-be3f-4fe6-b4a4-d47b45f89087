"""
Cut metadata tracking and management.

This module handles:
- Cut operation metadata collection
- Performance metrics calculation
- Cut history management
- Optimization suggestions
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from collections import defaultdict

from ..geometry.shapes import Cut, Sheet, Template

logger = logging.getLogger(__name__)


@dataclass
class CutMetrics:
    """Metrics for a single cut operation."""
    cut_id: str
    template_id: str
    sheet_id: str
    cut_area_mm2: float
    waste_area_mm2: float
    cut_length_mm: float
    utilization_ratio: float
    timestamp: str
    processing_time_ms: float = 0.0
    
    @property
    def efficiency_score(self) -> float:
        """Calculate efficiency score (0-100) based on utilization and waste."""
        # Higher utilization = better score
        utilization_score = self.utilization_ratio * 70
        
        # Lower waste ratio = better score
        total_area = self.cut_area_mm2 + self.waste_area_mm2
        waste_ratio = self.waste_area_mm2 / total_area if total_area > 0 else 1.0
        waste_score = (1.0 - waste_ratio) * 30
        
        return min(100.0, utilization_score + waste_score)


@dataclass
class ProjectMetrics:
    """Overall project cutting metrics."""
    total_cuts: int = 0
    total_cut_area_mm2: float = 0.0
    total_waste_area_mm2: float = 0.0
    total_cut_length_mm: float = 0.0
    average_utilization: float = 0.0
    average_efficiency: float = 0.0
    total_processing_time_ms: float = 0.0
    sheets_used: int = 0
    templates_cut: int = 0
    
    @property
    def overall_waste_ratio(self) -> float:
        """Calculate overall waste ratio."""
        total_material = self.total_cut_area_mm2 + self.total_waste_area_mm2
        return self.total_waste_area_mm2 / total_material if total_material > 0 else 0.0
    
    @property
    def material_efficiency(self) -> float:
        """Calculate material efficiency percentage."""
        return (1.0 - self.overall_waste_ratio) * 100


class MetadataTracker:
    """
    Tracks and analyzes cutting operation metadata.
    
    Features:
    - Cut operation metrics collection
    - Performance analysis and reporting
    - Optimization suggestions
    - Historical data management
    """
    
    def __init__(self, max_history_size: int = 1000):
        """
        Initialize metadata tracker.
        
        Args:
            max_history_size: Maximum number of cut records to keep
        """
        self.max_history_size = max_history_size
        self.cut_metrics: List[CutMetrics] = []
        self.sheet_usage: Dict[str, List[str]] = defaultdict(list)  # sheet_id -> cut_ids
        self.template_usage: Dict[str, List[str]] = defaultdict(list)  # template_id -> cut_ids
        
        logger.info("MetadataTracker initialized")
    
    def record_cut(self, cut: Cut, waste_area_mm2: float, 
                   processing_time_ms: float = 0.0) -> CutMetrics:
        """
        Record a cutting operation.
        
        Args:
            cut: Cut operation to record
            waste_area_mm2: Total waste area from remainder pieces
            processing_time_ms: Time taken to process the cut
            
        Returns:
            CutMetrics object for the recorded cut
        """
        # Calculate utilization ratio
        total_area = cut.cut_area + waste_area_mm2
        utilization_ratio = cut.cut_area / total_area if total_area > 0 else 0.0
        
        # Create metrics record
        metrics = CutMetrics(
            cut_id=cut.id,
            template_id=cut.template_id,
            sheet_id=cut.sheet_id,
            cut_area_mm2=cut.cut_area,
            waste_area_mm2=waste_area_mm2,
            cut_length_mm=cut.total_cut_length,
            utilization_ratio=utilization_ratio,
            timestamp=cut.timestamp or datetime.now().isoformat(),
            processing_time_ms=processing_time_ms
        )
        
        # Add to tracking
        self.cut_metrics.append(metrics)
        self.sheet_usage[cut.sheet_id].append(cut.id)
        self.template_usage[cut.template_id].append(cut.id)
        
        # Limit history size
        if len(self.cut_metrics) > self.max_history_size:
            oldest = self.cut_metrics.pop(0)
            self._cleanup_usage_tracking(oldest)
        
        logger.debug(f"Recorded cut {cut.id} with efficiency {metrics.efficiency_score:.1f}%")
        return metrics
    
    def _cleanup_usage_tracking(self, removed_metrics: CutMetrics):
        """Clean up usage tracking when removing old metrics."""
        # Remove from sheet usage
        if removed_metrics.cut_id in self.sheet_usage[removed_metrics.sheet_id]:
            self.sheet_usage[removed_metrics.sheet_id].remove(removed_metrics.cut_id)
            if not self.sheet_usage[removed_metrics.sheet_id]:
                del self.sheet_usage[removed_metrics.sheet_id]
        
        # Remove from template usage
        if removed_metrics.cut_id in self.template_usage[removed_metrics.template_id]:
            self.template_usage[removed_metrics.template_id].remove(removed_metrics.cut_id)
            if not self.template_usage[removed_metrics.template_id]:
                del self.template_usage[removed_metrics.template_id]
    
    def get_project_metrics(self) -> ProjectMetrics:
        """
        Calculate overall project metrics.
        
        Returns:
            ProjectMetrics with aggregated statistics
        """
        if not self.cut_metrics:
            return ProjectMetrics()
        
        total_cuts = len(self.cut_metrics)
        total_cut_area = sum(m.cut_area_mm2 for m in self.cut_metrics)
        total_waste_area = sum(m.waste_area_mm2 for m in self.cut_metrics)
        total_cut_length = sum(m.cut_length_mm for m in self.cut_metrics)
        total_processing_time = sum(m.processing_time_ms for m in self.cut_metrics)
        
        # Calculate averages
        utilizations = [m.utilization_ratio for m in self.cut_metrics]
        average_utilization = sum(utilizations) / len(utilizations)
        
        efficiencies = [m.efficiency_score for m in self.cut_metrics]
        average_efficiency = sum(efficiencies) / len(efficiencies)
        
        # Count unique sheets and templates
        sheets_used = len(self.sheet_usage)
        templates_cut = len(self.template_usage)
        
        return ProjectMetrics(
            total_cuts=total_cuts,
            total_cut_area_mm2=total_cut_area,
            total_waste_area_mm2=total_waste_area,
            total_cut_length_mm=total_cut_length,
            average_utilization=average_utilization,
            average_efficiency=average_efficiency,
            total_processing_time_ms=total_processing_time,
            sheets_used=sheets_used,
            templates_cut=templates_cut
        )
    
    def get_sheet_metrics(self, sheet_id: str) -> Dict[str, Any]:
        """
        Get metrics for a specific sheet.
        
        Args:
            sheet_id: ID of the sheet to analyze
            
        Returns:
            Dictionary with sheet-specific metrics
        """
        sheet_cuts = [m for m in self.cut_metrics if m.sheet_id == sheet_id]
        
        if not sheet_cuts:
            return {
                'sheet_id': sheet_id,
                'cuts_count': 0,
                'total_cut_area': 0.0,
                'total_waste_area': 0.0,
                'average_utilization': 0.0,
                'efficiency_scores': []
            }
        
        return {
            'sheet_id': sheet_id,
            'cuts_count': len(sheet_cuts),
            'total_cut_area': sum(m.cut_area_mm2 for m in sheet_cuts),
            'total_waste_area': sum(m.waste_area_mm2 for m in sheet_cuts),
            'average_utilization': sum(m.utilization_ratio for m in sheet_cuts) / len(sheet_cuts),
            'efficiency_scores': [m.efficiency_score for m in sheet_cuts],
            'cut_timeline': [(m.timestamp, m.cut_id) for m in sheet_cuts]
        }
    
    def get_template_metrics(self, template_id: str) -> Dict[str, Any]:
        """
        Get metrics for a specific template.
        
        Args:
            template_id: ID of the template to analyze
            
        Returns:
            Dictionary with template-specific metrics
        """
        template_cuts = [m for m in self.cut_metrics if m.template_id == template_id]
        
        if not template_cuts:
            return {
                'template_id': template_id,
                'cuts_count': 0,
                'total_area_cut': 0.0,
                'average_efficiency': 0.0,
                'sheets_used': []
            }
        
        sheets_used = list(set(m.sheet_id for m in template_cuts))
        
        return {
            'template_id': template_id,
            'cuts_count': len(template_cuts),
            'total_area_cut': sum(m.cut_area_mm2 for m in template_cuts),
            'average_efficiency': sum(m.efficiency_score for m in template_cuts) / len(template_cuts),
            'sheets_used': sheets_used,
            'cut_timeline': [(m.timestamp, m.sheet_id) for m in template_cuts]
        }
    
    def get_optimization_suggestions(self) -> List[Dict[str, Any]]:
        """
        Generate optimization suggestions based on cutting history.
        
        Returns:
            List of suggestion dictionaries
        """
        suggestions = []
        
        if len(self.cut_metrics) < 3:
            return suggestions
        
        metrics = self.get_project_metrics()
        
        # Low efficiency warning
        if metrics.average_efficiency < 60:
            suggestions.append({
                'type': 'efficiency',
                'severity': 'warning',
                'title': 'Low Cutting Efficiency',
                'description': f'Average efficiency is {metrics.average_efficiency:.1f}%. Consider optimizing template placement.',
                'recommendation': 'Try rotating templates or using different sheet orientations.'
            })
        
        # High waste warning
        if metrics.overall_waste_ratio > 0.4:
            suggestions.append({
                'type': 'waste',
                'severity': 'warning',
                'title': 'High Material Waste',
                'description': f'Waste ratio is {metrics.overall_waste_ratio*100:.1f}%. Consider using remainder pieces.',
                'recommendation': 'Save and reuse remainder pieces for smaller templates.'
            })
        
        # Template-specific suggestions
        for template_id in self.template_usage:
            template_metrics = self.get_template_metrics(template_id)
            if template_metrics['cuts_count'] > 1 and template_metrics['average_efficiency'] < 50:
                suggestions.append({
                    'type': 'template',
                    'severity': 'info',
                    'title': f'Template {template_id} Low Efficiency',
                    'description': f'This template has {template_metrics["average_efficiency"]:.1f}% efficiency.',
                    'recommendation': 'Consider grouping multiple instances or using different sheet sizes.'
                })
        
        return suggestions
    
    def export_metrics_report(self) -> Dict[str, Any]:
        """
        Export comprehensive metrics report.
        
        Returns:
            Complete metrics report dictionary
        """
        project_metrics = self.get_project_metrics()
        
        return {
            'report_timestamp': datetime.now().isoformat(),
            'project_metrics': project_metrics.__dict__,
            'cut_history': [m.__dict__ for m in self.cut_metrics[-50:]],  # Last 50 cuts
            'sheet_summary': {
                sheet_id: self.get_sheet_metrics(sheet_id)
                for sheet_id in list(self.sheet_usage.keys())[:20]  # Top 20 sheets
            },
            'template_summary': {
                template_id: self.get_template_metrics(template_id)
                for template_id in list(self.template_usage.keys())[:20]  # Top 20 templates
            },
            'optimization_suggestions': self.get_optimization_suggestions()
        }
