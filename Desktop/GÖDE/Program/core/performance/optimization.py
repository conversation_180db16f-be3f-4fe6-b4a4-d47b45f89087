"""
Performance optimization utilities for geometry and rendering operations.
"""

import logging
from typing import List, Dict, Any, Optional, Tu<PERSON>, Set
import math
from dataclasses import dataclass

from shapely.geometry import Polygon, Point as ShapelyPoint
from shapely.ops import unary_union
from shapely.prepared import prep

from core.geometry.shapes import Template, Sheet, Cut, Point

logger = logging.getLogger(__name__)


@dataclass
class OptimizationResult:
    """Result of an optimization operation."""
    success: bool
    original_count: int
    optimized_count: int
    reduction_percentage: float
    processing_time_ms: float
    message: str = ""


class GeometryOptimizer:
    """
    Optimizes geometry operations for better performance.
    
    Features:
    - Polygon simplification
    - Spatial clustering
    - Redundant geometry removal
    - Prepared geometry caching
    """
    
    def __init__(self, simplification_tolerance: float = 0.1):
        """
        Initialize geometry optimizer.
        
        Args:
            simplification_tolerance: Tolerance for polygon simplification
        """
        self.simplification_tolerance = simplification_tolerance
        self._prepared_cache: Dict[str, Any] = {}
        
        logger.info(f"GeometryOptimizer initialized with tolerance {simplification_tolerance}")
    
    def simplify_polygon(self, polygon: Polygon, tolerance: Optional[float] = None) -> Polygon:
        """
        Simplify a polygon to reduce vertex count.
        
        Args:
            polygon: Polygon to simplify
            tolerance: Simplification tolerance (uses default if None)
            
        Returns:
            Simplified polygon
        """
        try:
            if tolerance is None:
                tolerance = self.simplification_tolerance
            
            simplified = polygon.simplify(tolerance, preserve_topology=True)
            
            # Ensure the simplified polygon is still valid
            if not simplified.is_valid or simplified.is_empty:
                logger.warning("Simplification resulted in invalid polygon, returning original")
                return polygon
            
            return simplified
            
        except Exception as e:
            logger.warning(f"Failed to simplify polygon: {e}")
            return polygon
    
    def optimize_template_list(self, templates: List[Template]) -> OptimizationResult:
        """
        Optimize a list of templates by removing duplicates and simplifying.
        
        Args:
            templates: List of templates to optimize
            
        Returns:
            Optimization result
        """
        import time
        start_time = time.perf_counter()
        
        try:
            original_count = len(templates)
            
            if original_count == 0:
                return OptimizationResult(
                    success=True, original_count=0, optimized_count=0,
                    reduction_percentage=0.0, processing_time_ms=0.0,
                    message="No templates to optimize"
                )
            
            # Remove duplicate templates based on geometry
            unique_templates = self._remove_duplicate_templates(templates)
            
            # Simplify remaining templates
            for template in unique_templates:
                template.polygon = self.simplify_polygon(template.polygon)
            
            optimized_count = len(unique_templates)
            reduction_percentage = ((original_count - optimized_count) / original_count) * 100
            
            processing_time_ms = (time.perf_counter() - start_time) * 1000
            
            return OptimizationResult(
                success=True,
                original_count=original_count,
                optimized_count=optimized_count,
                reduction_percentage=reduction_percentage,
                processing_time_ms=processing_time_ms,
                message=f"Optimized {original_count} templates to {optimized_count}"
            )
            
        except Exception as e:
            processing_time_ms = (time.perf_counter() - start_time) * 1000
            logger.error(f"Template optimization failed: {e}")
            
            return OptimizationResult(
                success=False,
                original_count=len(templates),
                optimized_count=len(templates),
                reduction_percentage=0.0,
                processing_time_ms=processing_time_ms,
                message=f"Optimization failed: {str(e)}"
            )
    
    def optimize_sheet_list(self, sheets: List[Sheet]) -> OptimizationResult:
        """Optimize a list of sheets."""
        import time
        start_time = time.perf_counter()
        
        try:
            original_count = len(sheets)
            
            # Remove duplicate sheets and merge adjacent remainder pieces
            optimized_sheets = self._optimize_sheets(sheets)
            
            optimized_count = len(optimized_sheets)
            reduction_percentage = ((original_count - optimized_count) / original_count) * 100 if original_count > 0 else 0
            
            processing_time_ms = (time.perf_counter() - start_time) * 1000
            
            return OptimizationResult(
                success=True,
                original_count=original_count,
                optimized_count=optimized_count,
                reduction_percentage=reduction_percentage,
                processing_time_ms=processing_time_ms,
                message=f"Optimized {original_count} sheets to {optimized_count}"
            )
            
        except Exception as e:
            processing_time_ms = (time.perf_counter() - start_time) * 1000
            logger.error(f"Sheet optimization failed: {e}")
            
            return OptimizationResult(
                success=False,
                original_count=len(sheets),
                optimized_count=len(sheets),
                reduction_percentage=0.0,
                processing_time_ms=processing_time_ms,
                message=f"Optimization failed: {str(e)}"
            )
    
    def get_prepared_geometry(self, geometry_id: str, polygon: Polygon) -> Any:
        """
        Get prepared geometry for faster spatial operations.
        
        Args:
            geometry_id: Unique identifier for the geometry
            polygon: Polygon to prepare
            
        Returns:
            Prepared geometry object
        """
        try:
            if geometry_id not in self._prepared_cache:
                self._prepared_cache[geometry_id] = prep(polygon)
            
            return self._prepared_cache[geometry_id]
            
        except Exception as e:
            logger.warning(f"Failed to prepare geometry {geometry_id}: {e}")
            return polygon
    
    def clear_prepared_cache(self):
        """Clear the prepared geometry cache."""
        self._prepared_cache.clear()
        logger.debug("Prepared geometry cache cleared")
    
    def _remove_duplicate_templates(self, templates: List[Template]) -> List[Template]:
        """Remove duplicate templates based on geometry similarity."""
        if not templates:
            return []
        
        unique_templates = []
        seen_geometries = []
        
        for template in templates:
            is_duplicate = False
            
            for seen_geom in seen_geometries:
                if self._are_geometries_similar(template.polygon, seen_geom):
                    is_duplicate = True
                    break
            
            if not is_duplicate:
                unique_templates.append(template)
                seen_geometries.append(template.polygon)
        
        return unique_templates
    
    def _are_geometries_similar(self, geom1: Polygon, geom2: Polygon, tolerance: float = 1.0) -> bool:
        """Check if two geometries are similar within tolerance."""
        try:
            # Quick area check
            area_diff = abs(geom1.area - geom2.area)
            if area_diff > tolerance:
                return False
            
            # Check if geometries are approximately equal
            symmetric_diff = geom1.symmetric_difference(geom2)
            return symmetric_diff.area < tolerance
            
        except Exception:
            return False
    
    def _optimize_sheets(self, sheets: List[Sheet]) -> List[Sheet]:
        """Optimize sheet list by merging adjacent remainder pieces."""
        if not sheets:
            return []
        
        # Separate remainder and regular sheets
        regular_sheets = [s for s in sheets if not s.is_remainder]
        remainder_sheets = [s for s in sheets if s.is_remainder]
        
        # Try to merge adjacent remainder pieces
        merged_remainders = self._merge_adjacent_remainders(remainder_sheets)
        
        return regular_sheets + merged_remainders
    
    def _merge_adjacent_remainders(self, remainder_sheets: List[Sheet]) -> List[Sheet]:
        """Merge adjacent remainder pieces into larger pieces."""
        if len(remainder_sheets) < 2:
            return remainder_sheets
        
        merged = []
        used_indices = set()
        
        for i, sheet1 in enumerate(remainder_sheets):
            if i in used_indices:
                continue
            
            current_polygon = sheet1.polygon
            merged_with = [i]
            
            # Try to merge with other sheets
            for j, sheet2 in enumerate(remainder_sheets[i+1:], i+1):
                if j in used_indices:
                    continue
                
                # Check if sheets are adjacent (touching)
                if current_polygon.touches(sheet2.polygon):
                    try:
                        # Try to merge
                        union_polygon = unary_union([current_polygon, sheet2.polygon])
                        
                        # Check if union is a single polygon (not multipolygon)
                        if hasattr(union_polygon, 'exterior'):
                            current_polygon = union_polygon
                            merged_with.append(j)
                            used_indices.add(j)
                    except Exception:
                        # Merge failed, skip
                        pass
            
            # Create merged sheet
            if len(merged_with) > 1:
                # Multiple sheets merged
                merged_sheet = Sheet(
                    polygon=current_polygon,
                    is_remainder=True,
                    metadata={
                        'merged_from': len(merged_with),
                        'original_ids': [remainder_sheets[idx].id for idx in merged_with]
                    }
                )
            else:
                # No merge, use original
                merged_sheet = sheet1
            
            merged.append(merged_sheet)
            used_indices.update(merged_with)
        
        return merged


class RenderingOptimizer:
    """
    Optimizes rendering operations for better canvas performance.
    
    Features:
    - Level-of-detail (LOD) management
    - Viewport culling
    - Batch rendering optimization
    - Canvas object pooling
    """
    
    def __init__(self, lod_threshold: float = 0.1):
        """
        Initialize rendering optimizer.
        
        Args:
            lod_threshold: Threshold for level-of-detail switching
        """
        self.lod_threshold = lod_threshold
        self._object_pool: Dict[str, List[Any]] = {}
        
        logger.info(f"RenderingOptimizer initialized with LOD threshold {lod_threshold}")
    
    def calculate_lod_level(self, object_size: float, viewport_scale: float) -> int:
        """
        Calculate appropriate level-of-detail for an object.
        
        Args:
            object_size: Size of the object in world units
            viewport_scale: Current viewport scale factor
            
        Returns:
            LOD level (0 = highest detail, higher = lower detail)
        """
        try:
            # Calculate apparent size on screen
            apparent_size = object_size * viewport_scale
            
            if apparent_size < self.lod_threshold:
                return 3  # Very low detail
            elif apparent_size < self.lod_threshold * 5:
                return 2  # Low detail
            elif apparent_size < self.lod_threshold * 20:
                return 1  # Medium detail
            else:
                return 0  # High detail
                
        except Exception:
            return 0  # Default to high detail
    
    def cull_objects_to_viewport(self, objects: List[Any], viewport_bounds: Tuple[float, float, float, float]) -> List[Any]:
        """
        Cull objects outside the viewport.
        
        Args:
            objects: List of objects with polygon attribute
            viewport_bounds: (min_x, min_y, max_x, max_y) of viewport
            
        Returns:
            List of visible objects
        """
        try:
            min_x, min_y, max_x, max_y = viewport_bounds
            viewport_polygon = Polygon([
                (min_x, min_y), (max_x, min_y), 
                (max_x, max_y), (min_x, max_y)
            ])
            
            visible_objects = []
            
            for obj in objects:
                if hasattr(obj, 'polygon') and obj.polygon:
                    # Check if object intersects with viewport
                    if obj.polygon.intersects(viewport_polygon):
                        visible_objects.append(obj)
            
            return visible_objects
            
        except Exception as e:
            logger.warning(f"Viewport culling failed: {e}")
            return objects
    
    def optimize_polygon_for_rendering(self, polygon: Polygon, lod_level: int) -> Polygon:
        """
        Optimize polygon for rendering based on LOD level.
        
        Args:
            polygon: Original polygon
            lod_level: Level of detail (0 = highest)
            
        Returns:
            Optimized polygon
        """
        try:
            if lod_level == 0:
                return polygon  # No optimization for highest detail
            
            # Calculate simplification tolerance based on LOD
            tolerance = self.lod_threshold * (lod_level ** 2)
            
            simplified = polygon.simplify(tolerance, preserve_topology=True)
            
            if simplified.is_valid and not simplified.is_empty:
                return simplified
            else:
                return polygon
                
        except Exception as e:
            logger.warning(f"Polygon optimization failed: {e}")
            return polygon
    
    def batch_render_objects(self, objects: List[Any], viewport_bounds: Tuple[float, float, float, float], 
                           viewport_scale: float) -> Dict[str, List[Any]]:
        """
        Batch objects for optimized rendering.
        
        Args:
            objects: Objects to render
            viewport_bounds: Viewport boundaries
            viewport_scale: Current scale factor
            
        Returns:
            Dictionary of batched objects by type and LOD
        """
        try:
            # Cull objects outside viewport
            visible_objects = self.cull_objects_to_viewport(objects, viewport_bounds)
            
            # Group by type and LOD level
            batches = {}
            
            for obj in visible_objects:
                # Determine object type
                obj_type = type(obj).__name__
                
                # Calculate LOD level
                if hasattr(obj, 'polygon') and obj.polygon:
                    object_size = math.sqrt(obj.polygon.area)
                    lod_level = self.calculate_lod_level(object_size, viewport_scale)
                else:
                    lod_level = 0
                
                # Create batch key
                batch_key = f"{obj_type}_LOD{lod_level}"
                
                if batch_key not in batches:
                    batches[batch_key] = []
                
                batches[batch_key].append(obj)
            
            return batches
            
        except Exception as e:
            logger.warning(f"Batch rendering optimization failed: {e}")
            return {'default': objects}
    
    def get_render_statistics(self, batches: Dict[str, List[Any]]) -> Dict[str, Any]:
        """Get rendering statistics for batched objects."""
        try:
            total_objects = sum(len(batch) for batch in batches.values())
            
            stats = {
                'total_objects': total_objects,
                'batch_count': len(batches),
                'batches': {}
            }
            
            for batch_name, objects in batches.items():
                stats['batches'][batch_name] = {
                    'object_count': len(objects),
                    'percentage': (len(objects) / total_objects) * 100 if total_objects > 0 else 0
                }
            
            return stats
            
        except Exception as e:
            logger.warning(f"Failed to get render statistics: {e}")
            return {'error': str(e)}
