"""
Memory management for large-scale drywall optimization projects.
"""

import logging
import gc
import psutil
import weakref
from typing import Dict, List, Any, Optional, Set
from dataclasses import dataclass
from threading import Lock
import time

from core.geometry.shapes import Template, Sheet, Cut

logger = logging.getLogger(__name__)


@dataclass
class MemoryStats:
    """Memory usage statistics."""
    total_memory_mb: float
    used_memory_mb: float
    available_memory_mb: float
    process_memory_mb: float
    templates_count: int
    sheets_count: int
    cuts_count: int
    cache_size: int


class MemoryManager:
    """
    Manages memory usage for large-scale projects.
    
    Features:
    - Automatic garbage collection
    - Object caching with LRU eviction
    - Memory usage monitoring
    - Large project optimization
    - Weak reference tracking
    """
    
    def __init__(self, max_cache_size: int = 1000, memory_threshold_mb: float = 500.0):
        """
        Initialize memory manager.
        
        Args:
            max_cache_size: Maximum number of objects to cache
            memory_threshold_mb: Memory threshold for automatic cleanup
        """
        self.max_cache_size = max_cache_size
        self.memory_threshold_mb = memory_threshold_mb
        
        # Object caches with weak references
        self._template_cache: Dict[str, weakref.ref] = {}
        self._sheet_cache: Dict[str, weakref.ref] = {}
        self._cut_cache: Dict[str, weakref.ref] = {}
        
        # Access tracking for LRU
        self._access_times: Dict[str, float] = {}
        self._cache_lock = Lock()
        
        # Memory monitoring
        self._last_cleanup = time.time()
        self._cleanup_interval = 30.0  # seconds
        
        logger.info(f"MemoryManager initialized with cache size {max_cache_size}")
    
    def get_memory_stats(self) -> MemoryStats:
        """Get current memory usage statistics."""
        try:
            # System memory
            memory = psutil.virtual_memory()
            
            # Process memory
            process = psutil.Process()
            process_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # Object counts
            templates_count = len([ref for ref in self._template_cache.values() if ref() is not None])
            sheets_count = len([ref for ref in self._sheet_cache.values() if ref() is not None])
            cuts_count = len([ref for ref in self._cut_cache.values() if ref() is not None])
            
            return MemoryStats(
                total_memory_mb=memory.total / 1024 / 1024,
                used_memory_mb=memory.used / 1024 / 1024,
                available_memory_mb=memory.available / 1024 / 1024,
                process_memory_mb=process_memory,
                templates_count=templates_count,
                sheets_count=sheets_count,
                cuts_count=cuts_count,
                cache_size=len(self._access_times)
            )
            
        except Exception as e:
            logger.warning(f"Failed to get memory stats: {e}")
            return MemoryStats(0, 0, 0, 0, 0, 0, 0, 0)
    
    def cache_template(self, template: Template) -> bool:
        """
        Cache a template object.
        
        Args:
            template: Template to cache
            
        Returns:
            True if cached successfully
        """
        try:
            with self._cache_lock:
                # Check if we need to cleanup first
                if len(self._template_cache) >= self.max_cache_size:
                    self._cleanup_cache('template')
                
                # Create weak reference
                def cleanup_callback(ref):
                    self._remove_from_access_times(template.id)
                
                weak_ref = weakref.ref(template, cleanup_callback)
                self._template_cache[template.id] = weak_ref
                self._access_times[template.id] = time.time()
                
                return True
                
        except Exception as e:
            logger.warning(f"Failed to cache template {template.id}: {e}")
            return False
    
    def cache_sheet(self, sheet: Sheet) -> bool:
        """Cache a sheet object."""
        try:
            with self._cache_lock:
                if len(self._sheet_cache) >= self.max_cache_size:
                    self._cleanup_cache('sheet')
                
                def cleanup_callback(ref):
                    self._remove_from_access_times(sheet.id)
                
                weak_ref = weakref.ref(sheet, cleanup_callback)
                self._sheet_cache[sheet.id] = weak_ref
                self._access_times[sheet.id] = time.time()
                
                return True
                
        except Exception as e:
            logger.warning(f"Failed to cache sheet {sheet.id}: {e}")
            return False
    
    def cache_cut(self, cut: Cut) -> bool:
        """Cache a cut object."""
        try:
            with self._cache_lock:
                if len(self._cut_cache) >= self.max_cache_size:
                    self._cleanup_cache('cut')
                
                def cleanup_callback(ref):
                    self._remove_from_access_times(cut.id)
                
                weak_ref = weakref.ref(cut, cleanup_callback)
                self._cut_cache[cut.id] = weak_ref
                self._access_times[cut.id] = time.time()
                
                return True
                
        except Exception as e:
            logger.warning(f"Failed to cache cut {cut.id}: {e}")
            return False
    
    def get_template(self, template_id: str) -> Optional[Template]:
        """Get template from cache."""
        try:
            with self._cache_lock:
                weak_ref = self._template_cache.get(template_id)
                if weak_ref:
                    template = weak_ref()
                    if template:
                        self._access_times[template_id] = time.time()
                        return template
                    else:
                        # Object was garbage collected
                        del self._template_cache[template_id]
                
                return None
                
        except Exception as e:
            logger.warning(f"Failed to get template {template_id}: {e}")
            return None
    
    def get_sheet(self, sheet_id: str) -> Optional[Sheet]:
        """Get sheet from cache."""
        try:
            with self._cache_lock:
                weak_ref = self._sheet_cache.get(sheet_id)
                if weak_ref:
                    sheet = weak_ref()
                    if sheet:
                        self._access_times[sheet_id] = time.time()
                        return sheet
                    else:
                        del self._sheet_cache[sheet_id]
                
                return None
                
        except Exception as e:
            logger.warning(f"Failed to get sheet {sheet_id}: {e}")
            return None
    
    def get_cut(self, cut_id: str) -> Optional[Cut]:
        """Get cut from cache."""
        try:
            with self._cache_lock:
                weak_ref = self._cut_cache.get(cut_id)
                if weak_ref:
                    cut = weak_ref()
                    if cut:
                        self._access_times[cut_id] = time.time()
                        return cut
                    else:
                        del self._cut_cache[cut_id]
                
                return None
                
        except Exception as e:
            logger.warning(f"Failed to get cut {cut_id}: {e}")
            return None
    
    def _cleanup_cache(self, cache_type: str):
        """Cleanup cache using LRU eviction."""
        try:
            if cache_type == 'template':
                cache = self._template_cache
            elif cache_type == 'sheet':
                cache = self._sheet_cache
            elif cache_type == 'cut':
                cache = self._cut_cache
            else:
                return
            
            # Find oldest accessed items
            cache_items = [(obj_id, self._access_times.get(obj_id, 0)) 
                          for obj_id in cache.keys()]
            cache_items.sort(key=lambda x: x[1])  # Sort by access time
            
            # Remove oldest 25% of items
            remove_count = max(1, len(cache_items) // 4)
            for obj_id, _ in cache_items[:remove_count]:
                if obj_id in cache:
                    del cache[obj_id]
                self._remove_from_access_times(obj_id)
            
            logger.debug(f"Cleaned up {remove_count} items from {cache_type} cache")
            
        except Exception as e:
            logger.warning(f"Failed to cleanup {cache_type} cache: {e}")
    
    def _remove_from_access_times(self, obj_id: str):
        """Remove object ID from access times tracking."""
        try:
            if obj_id in self._access_times:
                del self._access_times[obj_id]
        except Exception:
            pass
    
    def force_garbage_collection(self) -> int:
        """Force garbage collection and return number of objects collected."""
        try:
            collected = gc.collect()
            logger.debug(f"Garbage collection freed {collected} objects")
            return collected
        except Exception as e:
            logger.warning(f"Failed to force garbage collection: {e}")
            return 0
    
    def check_memory_pressure(self) -> bool:
        """
        Check if system is under memory pressure.
        
        Returns:
            True if memory usage is above threshold
        """
        try:
            stats = self.get_memory_stats()
            return stats.process_memory_mb > self.memory_threshold_mb
        except Exception:
            return False
    
    def auto_cleanup(self):
        """Perform automatic cleanup if needed."""
        try:
            current_time = time.time()
            
            # Check if it's time for cleanup
            if current_time - self._last_cleanup < self._cleanup_interval:
                return
            
            self._last_cleanup = current_time
            
            # Check memory pressure
            if self.check_memory_pressure():
                logger.info("Memory pressure detected, performing cleanup")
                
                # Clean up caches
                with self._cache_lock:
                    self._cleanup_cache('template')
                    self._cleanup_cache('sheet')
                    self._cleanup_cache('cut')
                
                # Force garbage collection
                self.force_garbage_collection()
                
                # Log new stats
                stats = self.get_memory_stats()
                logger.info(f"After cleanup: {stats.process_memory_mb:.1f}MB used, "
                           f"{stats.cache_size} objects cached")
            
        except Exception as e:
            logger.warning(f"Auto cleanup failed: {e}")
    
    def clear_all_caches(self):
        """Clear all object caches."""
        try:
            with self._cache_lock:
                self._template_cache.clear()
                self._sheet_cache.clear()
                self._cut_cache.clear()
                self._access_times.clear()
            
            logger.info("All caches cleared")
            
        except Exception as e:
            logger.warning(f"Failed to clear caches: {e}")
    
    def get_cache_statistics(self) -> Dict[str, Any]:
        """Get detailed cache statistics."""
        try:
            with self._cache_lock:
                # Count live references
                live_templates = len([ref for ref in self._template_cache.values() if ref() is not None])
                live_sheets = len([ref for ref in self._sheet_cache.values() if ref() is not None])
                live_cuts = len([ref for ref in self._cut_cache.values() if ref() is not None])
                
                return {
                    'template_cache_size': len(self._template_cache),
                    'sheet_cache_size': len(self._sheet_cache),
                    'cut_cache_size': len(self._cut_cache),
                    'live_templates': live_templates,
                    'live_sheets': live_sheets,
                    'live_cuts': live_cuts,
                    'total_tracked_objects': len(self._access_times),
                    'max_cache_size': self.max_cache_size,
                    'memory_threshold_mb': self.memory_threshold_mb
                }
                
        except Exception as e:
            logger.warning(f"Failed to get cache statistics: {e}")
            return {}
