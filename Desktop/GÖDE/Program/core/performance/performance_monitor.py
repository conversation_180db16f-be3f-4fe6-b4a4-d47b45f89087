"""
Performance monitoring and profiling for the drywall optimization application.
"""

import logging
import time
import functools
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
import threading
from contextlib import contextmanager

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    duration_ms: float
    timestamp: float
    memory_before_mb: float = 0.0
    memory_after_mb: float = 0.0
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceStats:
    """Aggregated performance statistics."""
    operation_name: str
    total_calls: int
    total_duration_ms: float
    average_duration_ms: float
    min_duration_ms: float
    max_duration_ms: float
    last_duration_ms: float
    calls_per_second: float = 0.0
    memory_impact_mb: float = 0.0


class PerformanceMonitor:
    """
    Monitors and profiles application performance.
    
    Features:
    - Function execution timing
    - Memory usage tracking
    - Operation profiling
    - Performance statistics
    - Bottleneck identification
    """
    
    def __init__(self, max_history: int = 1000):
        """
        Initialize performance monitor.
        
        Args:
            max_history: Maximum number of metrics to keep in history
        """
        self.max_history = max_history
        
        # Metrics storage
        self._metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        self._lock = threading.Lock()
        
        # Active timers
        self._active_timers: Dict[str, float] = {}
        
        logger.info(f"PerformanceMonitor initialized with history size {max_history}")
    
    def record_metric(self, name: str, duration_ms: float, 
                     memory_before_mb: float = 0.0, memory_after_mb: float = 0.0,
                     metadata: Optional[Dict[str, Any]] = None):
        """
        Record a performance metric.
        
        Args:
            name: Operation name
            duration_ms: Duration in milliseconds
            memory_before_mb: Memory usage before operation
            memory_after_mb: Memory usage after operation
            metadata: Additional metadata
        """
        try:
            metric = PerformanceMetric(
                name=name,
                duration_ms=duration_ms,
                timestamp=time.time(),
                memory_before_mb=memory_before_mb,
                memory_after_mb=memory_after_mb,
                metadata=metadata or {}
            )
            
            with self._lock:
                self._metrics[name].append(metric)
            
            logger.debug(f"Recorded metric: {name} took {duration_ms:.2f}ms")
            
        except Exception as e:
            logger.warning(f"Failed to record metric {name}: {e}")
    
    @contextmanager
    def measure(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """
        Context manager for measuring operation performance.
        
        Args:
            operation_name: Name of the operation being measured
            metadata: Additional metadata to record
        """
        start_time = time.perf_counter()
        memory_before = self._get_memory_usage()
        
        try:
            yield
        finally:
            end_time = time.perf_counter()
            memory_after = self._get_memory_usage()
            duration_ms = (end_time - start_time) * 1000
            
            self.record_metric(
                operation_name, duration_ms, 
                memory_before, memory_after, metadata
            )
    
    def start_timer(self, operation_name: str):
        """Start a timer for an operation."""
        self._active_timers[operation_name] = time.perf_counter()
    
    def stop_timer(self, operation_name: str, metadata: Optional[Dict[str, Any]] = None):
        """Stop a timer and record the metric."""
        if operation_name not in self._active_timers:
            logger.warning(f"No active timer for operation: {operation_name}")
            return
        
        start_time = self._active_timers.pop(operation_name)
        duration_ms = (time.perf_counter() - start_time) * 1000
        
        self.record_metric(operation_name, duration_ms, metadata=metadata)
    
    def get_stats(self, operation_name: str) -> Optional[PerformanceStats]:
        """
        Get performance statistics for an operation.
        
        Args:
            operation_name: Name of the operation
            
        Returns:
            Performance statistics or None if no data
        """
        try:
            with self._lock:
                metrics = list(self._metrics.get(operation_name, []))
            
            if not metrics:
                return None
            
            durations = [m.duration_ms for m in metrics]
            total_calls = len(durations)
            total_duration = sum(durations)
            
            # Calculate time window for calls per second
            if total_calls > 1:
                time_window = metrics[-1].timestamp - metrics[0].timestamp
                calls_per_second = total_calls / max(time_window, 1.0)
            else:
                calls_per_second = 0.0
            
            # Calculate memory impact
            memory_impacts = [m.memory_after_mb - m.memory_before_mb for m in metrics if m.memory_before_mb > 0]
            avg_memory_impact = sum(memory_impacts) / len(memory_impacts) if memory_impacts else 0.0
            
            return PerformanceStats(
                operation_name=operation_name,
                total_calls=total_calls,
                total_duration_ms=total_duration,
                average_duration_ms=total_duration / total_calls,
                min_duration_ms=min(durations),
                max_duration_ms=max(durations),
                last_duration_ms=durations[-1],
                calls_per_second=calls_per_second,
                memory_impact_mb=avg_memory_impact
            )
            
        except Exception as e:
            logger.warning(f"Failed to get stats for {operation_name}: {e}")
            return None
    
    def get_all_stats(self) -> Dict[str, PerformanceStats]:
        """Get performance statistics for all operations."""
        stats = {}
        
        with self._lock:
            operation_names = list(self._metrics.keys())
        
        for name in operation_names:
            stat = self.get_stats(name)
            if stat:
                stats[name] = stat
        
        return stats
    
    def get_slowest_operations(self, limit: int = 10) -> List[PerformanceStats]:
        """
        Get the slowest operations by average duration.
        
        Args:
            limit: Maximum number of operations to return
            
        Returns:
            List of performance statistics sorted by average duration
        """
        all_stats = self.get_all_stats()
        sorted_stats = sorted(all_stats.values(), 
                            key=lambda s: s.average_duration_ms, 
                            reverse=True)
        return sorted_stats[:limit]
    
    def get_most_frequent_operations(self, limit: int = 10) -> List[PerformanceStats]:
        """
        Get the most frequently called operations.
        
        Args:
            limit: Maximum number of operations to return
            
        Returns:
            List of performance statistics sorted by call frequency
        """
        all_stats = self.get_all_stats()
        sorted_stats = sorted(all_stats.values(), 
                            key=lambda s: s.total_calls, 
                            reverse=True)
        return sorted_stats[:limit]
    
    def get_recent_metrics(self, operation_name: str, count: int = 10) -> List[PerformanceMetric]:
        """
        Get recent metrics for an operation.
        
        Args:
            operation_name: Name of the operation
            count: Number of recent metrics to return
            
        Returns:
            List of recent performance metrics
        """
        try:
            with self._lock:
                metrics = list(self._metrics.get(operation_name, []))
            
            return metrics[-count:] if metrics else []
            
        except Exception as e:
            logger.warning(f"Failed to get recent metrics for {operation_name}: {e}")
            return []
    
    def clear_metrics(self, operation_name: Optional[str] = None):
        """
        Clear metrics for an operation or all operations.
        
        Args:
            operation_name: Name of operation to clear, or None for all
        """
        try:
            with self._lock:
                if operation_name:
                    if operation_name in self._metrics:
                        self._metrics[operation_name].clear()
                        logger.info(f"Cleared metrics for {operation_name}")
                else:
                    self._metrics.clear()
                    logger.info("Cleared all metrics")
                    
        except Exception as e:
            logger.warning(f"Failed to clear metrics: {e}")
    
    def export_metrics(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """
        Export metrics for analysis.
        
        Args:
            operation_name: Name of operation to export, or None for all
            
        Returns:
            Dictionary containing metrics data
        """
        try:
            with self._lock:
                if operation_name:
                    metrics = list(self._metrics.get(operation_name, []))
                    return {
                        operation_name: [
                            {
                                'duration_ms': m.duration_ms,
                                'timestamp': m.timestamp,
                                'memory_before_mb': m.memory_before_mb,
                                'memory_after_mb': m.memory_after_mb,
                                'metadata': m.metadata
                            }
                            for m in metrics
                        ]
                    }
                else:
                    return {
                        name: [
                            {
                                'duration_ms': m.duration_ms,
                                'timestamp': m.timestamp,
                                'memory_before_mb': m.memory_before_mb,
                                'memory_after_mb': m.memory_after_mb,
                                'metadata': m.metadata
                            }
                            for m in metrics
                        ]
                        for name, metrics in self._metrics.items()
                    }
                    
        except Exception as e:
            logger.warning(f"Failed to export metrics: {e}")
            return {}
    
    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024
        except Exception:
            return 0.0
    
    def profile_function(self, func: Callable) -> Callable:
        """
        Decorator to profile function execution.
        
        Args:
            func: Function to profile
            
        Returns:
            Wrapped function with profiling
        """
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            operation_name = f"{func.__module__}.{func.__name__}"
            
            with self.measure(operation_name):
                return func(*args, **kwargs)
        
        return wrapper
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get a comprehensive performance summary."""
        try:
            all_stats = self.get_all_stats()
            
            if not all_stats:
                return {'message': 'No performance data available'}
            
            # Calculate totals
            total_operations = len(all_stats)
            total_calls = sum(s.total_calls for s in all_stats.values())
            total_time_ms = sum(s.total_duration_ms for s in all_stats.values())
            
            # Find extremes
            slowest = max(all_stats.values(), key=lambda s: s.average_duration_ms)
            fastest = min(all_stats.values(), key=lambda s: s.average_duration_ms)
            most_called = max(all_stats.values(), key=lambda s: s.total_calls)
            
            return {
                'summary': {
                    'total_operations': total_operations,
                    'total_calls': total_calls,
                    'total_time_ms': total_time_ms,
                    'average_time_per_call_ms': total_time_ms / total_calls if total_calls > 0 else 0
                },
                'extremes': {
                    'slowest_operation': {
                        'name': slowest.operation_name,
                        'average_duration_ms': slowest.average_duration_ms
                    },
                    'fastest_operation': {
                        'name': fastest.operation_name,
                        'average_duration_ms': fastest.average_duration_ms
                    },
                    'most_called_operation': {
                        'name': most_called.operation_name,
                        'total_calls': most_called.total_calls
                    }
                },
                'top_operations': {
                    'slowest': [
                        {'name': s.operation_name, 'avg_duration_ms': s.average_duration_ms}
                        for s in self.get_slowest_operations(5)
                    ],
                    'most_frequent': [
                        {'name': s.operation_name, 'total_calls': s.total_calls}
                        for s in self.get_most_frequent_operations(5)
                    ]
                }
            }
            
        except Exception as e:
            logger.warning(f"Failed to get performance summary: {e}")
            return {'error': str(e)}
