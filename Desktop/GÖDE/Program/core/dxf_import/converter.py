"""
DXF to internal format converter.
"""

import logging
from typing import List, Dict, Any, Optional
from pathlib import Path

from .parser import DXFParser
from ..geometry.shapes import Template


logger = logging.getLogger(__name__)


class DXFConverter:
    """High-level converter for DXF files to internal template format."""
    
    def __init__(self):
        """Initialize converter."""
        self.parser = DXFParser()
    
    def import_dxf(self, file_path: str, layer_name: Optional[str] = None, 
                   units: str = 'mm', scale_factor: float = 1.0) -> Dict[str, Any]:
        """
        Import DXF file and convert to templates.
        
        Args:
            file_path: Path to DXF file
            layer_name: Specific layer to import (None for all)
            units: Target units for coordinates
            scale_factor: Additional scale factor to apply
            
        Returns:
            Dictionary with templates and import metadata
        """
        try:
            # Validate file
            if not Path(file_path).exists():
                raise FileNotFoundError(f"DXF file not found: {file_path}")
            
            # Parse DXF file
            templates, metadata = self.parser.parse_file(file_path, layer_name, units)
            
            # Apply additional scaling if needed
            if scale_factor != 1.0:
                templates = self._apply_scale_factor(templates, scale_factor)
                metadata['additional_scale_factor'] = scale_factor
            
            # Validate templates
            valid_templates = self._validate_templates(templates)
            
            # Prepare result
            result = {
                'templates': valid_templates,
                'metadata': metadata,
                'import_summary': {
                    'total_templates': len(valid_templates),
                    'source_file': metadata.get('source_file', ''),
                    'layers_processed': metadata.get('layers', []),
                    'units': units,
                    'scale_factor': scale_factor
                }
            }
            
            logger.info(f"Successfully imported {len(valid_templates)} templates from {file_path}")
            return result
            
        except Exception as e:
            logger.error(f"Failed to import DXF file {file_path}: {e}")
            raise
    
    def _apply_scale_factor(self, templates: List[Template], scale_factor: float) -> List[Template]:
        """Apply additional scale factor to templates."""
        from shapely.affinity import scale
        
        scaled_templates = []
        for template in templates:
            try:
                scaled_polygon = scale(template.polygon, xfact=scale_factor, yfact=scale_factor)
                scaled_template = Template(
                    id=template.id,
                    polygon=scaled_polygon,
                    metadata=template.metadata.copy(),
                    position=template.position,
                    rotation=template.rotation
                )
                scaled_templates.append(scaled_template)
            except Exception as e:
                logger.warning(f"Failed to scale template {template.id}: {e}")
        
        return scaled_templates
    
    def _validate_templates(self, templates: List[Template]) -> List[Template]:
        """Validate and filter templates."""
        valid_templates = []
        
        for template in templates:
            try:
                # Check if polygon is valid
                if not template.polygon.is_valid:
                    logger.warning(f"Invalid polygon in template {template.id}, attempting to fix")
                    # Try to fix the polygon
                    fixed_polygon = template.polygon.buffer(0)
                    if fixed_polygon.is_valid:
                        template.polygon = fixed_polygon
                        logger.info(f"Fixed polygon for template {template.id}")
                    else:
                        logger.warning(f"Could not fix polygon for template {template.id}, skipping")
                        continue
                
                # Check minimum area (avoid tiny polygons)
                min_area = 1.0  # 1 square unit minimum
                if template.polygon.area < min_area:
                    logger.warning(f"Template {template.id} area too small ({template.polygon.area}), skipping")
                    continue
                
                # Check if polygon is closed
                if not template.polygon.exterior.is_closed:
                    logger.warning(f"Template {template.id} polygon not closed, skipping")
                    continue
                
                valid_templates.append(template)
                
            except Exception as e:
                logger.warning(f"Validation failed for template {template.id}: {e}")
        
        logger.info(f"Validated {len(valid_templates)} out of {len(templates)} templates")
        return valid_templates
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported file formats."""
        return ['.dxf', '.DXF']
    
    def validate_file(self, file_path: str) -> bool:
        """
        Validate if file can be imported.
        
        Args:
            file_path: Path to file
            
        Returns:
            True if file can be imported
        """
        try:
            path = Path(file_path)
            
            # Check if file exists
            if not path.exists():
                return False
            
            # Check file extension
            if path.suffix.lower() not in ['.dxf']:
                return False
            
            # Try to open with ezdxf (basic validation)
            import ezdxf
            doc = ezdxf.readfile(file_path)
            
            return True
            
        except Exception as e:
            logger.debug(f"File validation failed for {file_path}: {e}")
            return False
    
    def get_file_info(self, file_path: str) -> Dict[str, Any]:
        """
        Get information about a DXF file without full import.
        
        Args:
            file_path: Path to DXF file
            
        Returns:
            Dictionary with file information
        """
        try:
            import ezdxf
            doc = ezdxf.readfile(file_path)
            
            # Get basic info
            info = {
                'file_name': Path(file_path).name,
                'file_size': Path(file_path).stat().st_size,
                'dxf_version': doc.dxfversion,
                'layers': [],
                'entity_counts': {},
                'estimated_templates': 0
            }
            
            # Get layer information
            for layer in doc.layers:
                info['layers'].append({
                    'name': layer.dxf.name,
                    'color': layer.dxf.color
                })
            
            # Count entities
            msp = doc.modelspace()
            closed_polylines = 0
            
            for entity in msp:
                entity_type = entity.dxftype()
                info['entity_counts'][entity_type] = info['entity_counts'].get(entity_type, 0) + 1
                
                # Count potential templates (closed polylines)
                if entity_type in ['LWPOLYLINE', 'POLYLINE']:
                    if hasattr(entity, 'closed') and entity.closed:
                        closed_polylines += 1
                    elif hasattr(entity, 'is_closed') and entity.is_closed:
                        closed_polylines += 1
                elif entity_type == 'CIRCLE':
                    closed_polylines += 1
            
            info['estimated_templates'] = closed_polylines
            
            return info
            
        except Exception as e:
            logger.error(f"Failed to get file info for {file_path}: {e}")
            return {
                'file_name': Path(file_path).name,
                'error': str(e)
            }
