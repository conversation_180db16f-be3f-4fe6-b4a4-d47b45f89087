"""
Geometry module for 2D shape operations.

This module provides classes and functions for handling 2D geometry:
- Basic shapes (Point, Template, Sheet)
- Polygon operations (clipping, intersection, union)
- Spatial indexing for performance
- Snapping and alignment operations
"""

from .shapes import Point, Template, Sheet, Cut
from .operations import GeometryOperations
from .spatial import SpatialIndex

__all__ = [
    'Point',
    'Template', 
    'Sheet',
    'Cut',
    'GeometryOperations',
    'SpatialIndex'
]
