"""
Geometry operations for polygon clipping, snapping, and spatial calculations.
"""

from typing import List, <PERSON><PERSON>, Optional, Union
import math
from shapely.geometry import <PERSON>ygon, Point as ShapelyPoint, LineString
from shapely.ops import unary_union
from shapely.affinity import translate, rotate

from .shapes import Point, Template, Sheet, Cut, LineSegment


class GeometryOperations:
    """Class containing static methods for geometry operations."""
    
    @staticmethod
    def snap_point_to_point(point: Point, target: Point, tolerance: float) -> Optional[Point]:
        """
        Snap a point to a target point if within tolerance.
        
        Args:
            point: Point to snap
            target: Target point to snap to
            tolerance: Maximum distance for snapping
            
        Returns:
            Snapped point if within tolerance, None otherwise
        """
        distance = point.distance_to(target)
        if distance <= tolerance:
            return target
        return None
    
    @staticmethod
    def snap_point_to_line(point: Point, line: LineSegment, tolerance: float) -> Optional[Point]:
        """
        Snap a point to the nearest point on a line segment if within tolerance.
        
        Args:
            point: Point to snap
            line: Line segment to snap to
            tolerance: Maximum distance for snapping
            
        Returns:
            Snapped point if within tolerance, None otherwise
        """
        # Convert to Shapely objects for easier calculation
        shapely_point = point.to_shapely()
        shapely_line = line.to_shapely()
        
        # Find nearest point on line
        nearest_point = shapely_line.interpolate(shapely_line.project(shapely_point))
        nearest = Point(nearest_point.x, nearest_point.y)
        
        # Check if within tolerance
        distance = point.distance_to(nearest)
        if distance <= tolerance:
            return nearest
        return None
    
    @staticmethod
    def get_polygon_edges(polygon: Polygon) -> List[LineSegment]:
        """
        Extract all edges from a polygon as line segments.
        
        Args:
            polygon: Input polygon
            
        Returns:
            List of line segments representing polygon edges
        """
        coords = list(polygon.exterior.coords)
        edges = []
        
        for i in range(len(coords) - 1):
            start = Point(coords[i][0], coords[i][1])
            end = Point(coords[i + 1][0], coords[i + 1][1])
            edges.append(LineSegment(start, end))
        
        return edges
    
    @staticmethod
    def get_polygon_vertices(polygon: Polygon) -> List[Point]:
        """
        Extract all vertices from a polygon.
        
        Args:
            polygon: Input polygon
            
        Returns:
            List of points representing polygon vertices
        """
        coords = list(polygon.exterior.coords)[:-1]  # Exclude duplicate last point
        return [Point(x, y) for x, y in coords]
    
    @staticmethod
    def clip_polygon(subject: Polygon, clip: Polygon) -> Tuple[Optional[Polygon], List[Polygon]]:
        """
        Clip subject polygon with clip polygon.
        
        Args:
            subject: Polygon to be clipped (e.g., sheet)
            clip: Clipping polygon (e.g., template)
            
        Returns:
            Tuple of (intersection, remainders)
            - intersection: The overlapping area (cut piece)
            - remainders: List of remaining pieces after clipping
        """
        try:
            # Get intersection (the cut piece)
            intersection = subject.intersection(clip)
            
            # Handle case where intersection is not a polygon
            if intersection.is_empty:
                return None, [subject]
            
            if not isinstance(intersection, Polygon):
                # If intersection is MultiPolygon or other, take the largest piece
                if hasattr(intersection, 'geoms'):
                    polygons = [geom for geom in intersection.geoms if isinstance(geom, Polygon)]
                    if polygons:
                        intersection = max(polygons, key=lambda p: p.area)
                    else:
                        return None, [subject]
                else:
                    return None, [subject]
            
            # Get remainder (subject minus clip)
            remainder = subject.difference(clip)
            
            # Handle remainder
            remainders = []
            if not remainder.is_empty:
                if isinstance(remainder, Polygon):
                    remainders = [remainder]
                elif hasattr(remainder, 'geoms'):
                    remainders = [geom for geom in remainder.geoms if isinstance(geom, Polygon)]
            
            return intersection, remainders
            
        except Exception as e:
            # If clipping fails, return original subject as remainder
            print(f"Clipping operation failed: {e}")
            return None, [subject]
    
    @staticmethod
    def calculate_cut_lines(template_polygon: Polygon, sheet_polygon: Polygon) -> List[LineSegment]:
        """
        Calculate the cut lines needed to extract template from sheet.
        
        Args:
            template_polygon: The template shape
            sheet_polygon: The sheet being cut
            
        Returns:
            List of line segments representing cut lines
        """
        try:
            # Get the intersection boundary
            intersection = template_polygon.intersection(sheet_polygon)
            
            if intersection.is_empty or not isinstance(intersection, Polygon):
                return []
            
            # Get the boundary of the intersection
            boundary = intersection.boundary
            
            if hasattr(boundary, 'geoms'):
                # MultiLineString case
                lines = []
                for geom in boundary.geoms:
                    if isinstance(geom, LineString):
                        coords = list(geom.coords)
                        for i in range(len(coords) - 1):
                            start = Point(coords[i][0], coords[i][1])
                            end = Point(coords[i + 1][0], coords[i + 1][1])
                            lines.append(LineSegment(start, end))
                return lines
            else:
                # Single LineString case
                coords = list(boundary.coords)
                lines = []
                for i in range(len(coords) - 1):
                    start = Point(coords[i][0], coords[i][1])
                    end = Point(coords[i + 1][0], coords[i + 1][1])
                    lines.append(LineSegment(start, end))
                return lines
                
        except Exception as e:
            print(f"Cut line calculation failed: {e}")
            return []
    
    @staticmethod
    def polygon_contains_point(polygon: Polygon, point: Point) -> bool:
        """
        Check if a polygon contains a point.
        
        Args:
            polygon: The polygon to check
            point: The point to test
            
        Returns:
            True if polygon contains point
        """
        return polygon.contains(point.to_shapely())
    
    @staticmethod
    def polygons_intersect(poly1: Polygon, poly2: Polygon) -> bool:
        """
        Check if two polygons intersect.
        
        Args:
            poly1: First polygon
            poly2: Second polygon
            
        Returns:
            True if polygons intersect
        """
        return poly1.intersects(poly2)
    
    @staticmethod
    def calculate_polygon_area(polygon: Polygon) -> float:
        """
        Calculate the area of a polygon.
        
        Args:
            polygon: Input polygon
            
        Returns:
            Area in square units
        """
        return polygon.area
    
    @staticmethod
    def get_polygon_centroid(polygon: Polygon) -> Point:
        """
        Get the centroid of a polygon.
        
        Args:
            polygon: Input polygon
            
        Returns:
            Centroid point
        """
        centroid = polygon.centroid
        return Point(centroid.x, centroid.y)
