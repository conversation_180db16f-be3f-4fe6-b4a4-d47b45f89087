"""
Spatial indexing for efficient geometry operations.
"""

from typing import List, Tuple, Any, Optional, Union
from shapely.geometry import Polygon
from shapely.strtree import STRtree

from .shapes import Template, Sheet, Point


class SpatialIndex:
    """
    Spatial index for efficient geometric queries.
    Uses Shapely's STRtree for R-tree based spatial indexing.
    """
    
    def __init__(self):
        """Initialize empty spatial index."""
        self._geometries = []
        self._objects = []
        self._tree = None
        self._dirty = True
    
    def add_template(self, template: Template) -> None:
        """
        Add a template to the spatial index.
        
        Args:
            template: Template to add
        """
        self._geometries.append(template.polygon)
        self._objects.append(('template', template))
        self._dirty = True
    
    def add_sheet(self, sheet: Sheet) -> None:
        """
        Add a sheet to the spatial index.
        
        Args:
            sheet: Sheet to add
        """
        self._geometries.append(sheet.polygon)
        self._objects.append(('sheet', sheet))
        self._dirty = True
    
    def remove_object(self, obj: Union[Template, Sheet]) -> bool:
        """
        Remove an object from the spatial index.
        
        Args:
            obj: Object to remove
            
        Returns:
            True if object was found and removed
        """
        for i, (obj_type, stored_obj) in enumerate(self._objects):
            if stored_obj.id == obj.id:
                del self._geometries[i]
                del self._objects[i]
                self._dirty = True
                return True
        return False
    
    def clear(self) -> None:
        """Clear all objects from the spatial index."""
        self._geometries.clear()
        self._objects.clear()
        self._tree = None
        self._dirty = True
    
    def _rebuild_tree(self) -> None:
        """Rebuild the spatial tree if dirty."""
        if self._dirty and self._geometries:
            self._tree = STRtree(self._geometries)
            self._dirty = False
        elif not self._geometries:
            self._tree = None
            self._dirty = False
    
    def query_point(self, point: Point, buffer_distance: float = 0.0) -> List[Tuple[str, Union[Template, Sheet]]]:
        """
        Query objects near a point.
        
        Args:
            point: Query point
            buffer_distance: Buffer distance around point
            
        Returns:
            List of (object_type, object) tuples
        """
        self._rebuild_tree()
        
        if self._tree is None:
            return []
        
        # Create query geometry
        query_geom = point.to_shapely()
        if buffer_distance > 0:
            query_geom = query_geom.buffer(buffer_distance)
        
        # Query the tree
        indices = self._tree.query(query_geom)
        
        results = []
        for idx in indices:
            if idx < len(self._objects):
                obj_type, obj = self._objects[idx]
                # Additional check for actual intersection
                if self._geometries[idx].intersects(query_geom):
                    results.append((obj_type, obj))
        
        return results
    
    def query_polygon(self, polygon: Polygon) -> List[Tuple[str, Union[Template, Sheet]]]:
        """
        Query objects that intersect with a polygon.
        
        Args:
            polygon: Query polygon
            
        Returns:
            List of (object_type, object) tuples
        """
        self._rebuild_tree()
        
        if self._tree is None:
            return []
        
        # Query the tree
        indices = self._tree.query(polygon)
        
        results = []
        for idx in indices:
            if idx < len(self._objects):
                obj_type, obj = self._objects[idx]
                # Additional check for actual intersection
                if self._geometries[idx].intersects(polygon):
                    results.append((obj_type, obj))
        
        return results
    
    def query_bbox(self, minx: float, miny: float, maxx: float, maxy: float) -> List[Tuple[str, Union[Template, Sheet]]]:
        """
        Query objects within a bounding box.
        
        Args:
            minx, miny, maxx, maxy: Bounding box coordinates
            
        Returns:
            List of (object_type, object) tuples
        """
        from shapely.geometry import box
        bbox_polygon = box(minx, miny, maxx, maxy)
        return self.query_polygon(bbox_polygon)
    
    def find_nearest(self, point: Point, max_distance: float = float('inf')) -> Optional[Tuple[str, Union[Template, Sheet], float]]:
        """
        Find the nearest object to a point.
        
        Args:
            point: Query point
            max_distance: Maximum search distance
            
        Returns:
            Tuple of (object_type, object, distance) or None if no objects found
        """
        self._rebuild_tree()
        
        if self._tree is None:
            return None
        
        query_geom = point.to_shapely()
        nearest_obj = None
        nearest_distance = max_distance
        nearest_type = None
        
        # Query with a reasonable buffer first
        buffer_distance = min(max_distance, 1000.0)  # Reasonable initial buffer
        candidates = self.query_point(point, buffer_distance)
        
        for obj_type, obj in candidates:
            distance = query_geom.distance(obj.polygon)
            if distance < nearest_distance:
                nearest_distance = distance
                nearest_obj = obj
                nearest_type = obj_type
        
        if nearest_obj is not None:
            return (nearest_type, nearest_obj, nearest_distance)
        
        return None
    
    def get_all_templates(self) -> List[Template]:
        """
        Get all templates in the index.
        
        Returns:
            List of all templates
        """
        return [obj for obj_type, obj in self._objects if obj_type == 'template']
    
    def get_all_sheets(self) -> List[Sheet]:
        """
        Get all sheets in the index.
        
        Returns:
            List of all sheets
        """
        return [obj for obj_type, obj in self._objects if obj_type == 'sheet']
    
    def get_object_count(self) -> Tuple[int, int]:
        """
        Get count of objects in the index.
        
        Returns:
            Tuple of (template_count, sheet_count)
        """
        template_count = sum(1 for obj_type, _ in self._objects if obj_type == 'template')
        sheet_count = sum(1 for obj_type, _ in self._objects if obj_type == 'sheet')
        return template_count, sheet_count
