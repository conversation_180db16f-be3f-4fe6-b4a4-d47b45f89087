"""
Basic geometry shapes and classes for the drywall optimization application.
"""

import uuid
from typing import List, Tu<PERSON>, Dict, Any, Optional
from dataclasses import dataclass, field
from shapely.geometry import <PERSON>y<PERSON>, Point as ShapelyPoint, LineString
from shapely.affinity import translate, rotate
import json


@dataclass
class Point:
    """Represents a 2D point with x, y coordinates."""
    x: float
    y: float
    
    def __post_init__(self):
        """Validate coordinates."""
        if not isinstance(self.x, (int, float)) or not isinstance(self.y, (int, float)):
            raise ValueError("Point coordinates must be numeric")
    
    def distance_to(self, other: 'Point') -> float:
        """Calculate distance to another point."""
        return ((self.x - other.x) ** 2 + (self.y - other.y) ** 2) ** 0.5
    
    def to_tuple(self) -> Tuple[float, float]:
        """Convert to tuple representation."""
        return (self.x, self.y)
    
    def to_shapely(self) -> ShapelyPoint:
        """Convert to Shapely Point."""
        return ShapelyPoint(self.x, self.y)


@dataclass
class LineSegment:
    """Represents a line segment with start and end points."""
    start: Point
    end: Point
    
    @property
    def length(self) -> float:
        """Calculate the length of the line segment."""
        return self.start.distance_to(self.end)
    
    @property
    def length_cm(self) -> float:
        """Get length in centimeters (assuming coordinates are in mm)."""
        return self.length / 10.0
    
    def to_shapely(self) -> LineString:
        """Convert to Shapely LineString."""
        return LineString([self.start.to_tuple(), self.end.to_tuple()])


@dataclass
class Template:
    """Represents a template shape extracted from DXF."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    polygon: Optional[Polygon] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    position: Point = field(default_factory=lambda: Point(0, 0))
    rotation: float = 0.0  # degrees
    
    def __post_init__(self):
        """Validate template data."""
        if self.polygon is None:
            raise ValueError("Template must have a polygon")
        if not self.polygon.is_valid:
            raise ValueError("Template polygon must be valid")
        if not self.polygon.exterior.is_closed:
            raise ValueError("Template polygon must be closed")
    
    @property
    def area(self) -> float:
        """Get the area of the template."""
        return self.polygon.area
    
    @property
    def bounds(self) -> Tuple[float, float, float, float]:
        """Get bounding box (minx, miny, maxx, maxy)."""
        return self.polygon.bounds
    
    def move_to(self, position: Point) -> 'Template':
        """Create a new template moved to the specified position."""
        current_centroid = self.polygon.centroid
        dx = position.x - current_centroid.x
        dy = position.y - current_centroid.y
        
        new_polygon = translate(self.polygon, xoff=dx, yoff=dy)
        
        return Template(
            id=self.id,
            polygon=new_polygon,
            metadata=self.metadata.copy(),
            position=position,
            rotation=self.rotation
        )
    
    def rotate_by(self, angle_degrees: float, origin: Optional[Point] = None) -> 'Template':
        """Create a new template rotated by the specified angle."""
        if origin is None:
            origin_point = self.polygon.centroid
        else:
            origin_point = origin.to_shapely()
        
        new_polygon = rotate(
            self.polygon, 
            angle_degrees, 
            origin=(origin_point.x, origin_point.y)
        )
        
        return Template(
            id=self.id,
            polygon=new_polygon,
            metadata=self.metadata.copy(),
            position=self.position,
            rotation=(self.rotation + angle_degrees) % 360
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        coords = list(self.polygon.exterior.coords)
        return {
            'id': self.id,
            'polygon': {'coordinates': coords},
            'metadata': self.metadata,
            'position': {'x': self.position.x, 'y': self.position.y},
            'rotation': self.rotation
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Template':
        """Create Template from dictionary."""
        coords = data['polygon']['coordinates']
        polygon = Polygon(coords)
        position = Point(data['position']['x'], data['position']['y'])
        
        return cls(
            id=data['id'],
            polygon=polygon,
            metadata=data.get('metadata', {}),
            position=position,
            rotation=data.get('rotation', 0.0)
        )


@dataclass
class Sheet:
    """Represents a drywall sheet that can be cut."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    width: float = 0.0
    height: float = 0.0
    polygon: Optional[Polygon] = None
    position: Point = field(default_factory=lambda: Point(0, 0))
    rotation: float = 0.0  # degrees
    is_remainder: bool = False
    parent_sheet_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Initialize polygon if not provided."""
        if self.polygon is None and self.width > 0 and self.height > 0:
            # Create rectangular polygon
            coords = [
                (0, 0),
                (self.width, 0),
                (self.width, self.height),
                (0, self.height),
                (0, 0)
            ]
            self.polygon = Polygon(coords)

        if self.polygon is None:
            raise ValueError("Sheet must have either polygon or width/height")

        if not self.polygon.is_valid:
            raise ValueError("Sheet polygon must be valid")

    @property
    def area(self) -> float:
        """Get the area of the sheet."""
        return self.polygon.area

    @property
    def bounds(self) -> Tuple[float, float, float, float]:
        """Get bounding box (minx, miny, maxx, maxy)."""
        return self.polygon.bounds

    def move_to(self, position: Point) -> 'Sheet':
        """Create a new sheet moved to the specified position."""
        current_centroid = self.polygon.centroid
        dx = position.x - current_centroid.x
        dy = position.y - current_centroid.y

        new_polygon = translate(self.polygon, xoff=dx, yoff=dy)

        return Sheet(
            id=self.id,
            width=self.width,
            height=self.height,
            polygon=new_polygon,
            position=position,
            rotation=self.rotation,
            is_remainder=self.is_remainder,
            parent_sheet_id=self.parent_sheet_id,
            metadata=self.metadata.copy()
        )

    def rotate_by(self, angle_degrees: float, origin: Optional[Point] = None) -> 'Sheet':
        """Create a new sheet rotated by the specified angle."""
        if origin is None:
            origin_point = self.polygon.centroid
        else:
            origin_point = origin.to_shapely()

        new_polygon = rotate(
            self.polygon,
            angle_degrees,
            origin=(origin_point.x, origin_point.y)
        )

        return Sheet(
            id=self.id,
            width=self.width,
            height=self.height,
            polygon=new_polygon,
            position=self.position,
            rotation=(self.rotation + angle_degrees) % 360,
            is_remainder=self.is_remainder,
            parent_sheet_id=self.parent_sheet_id,
            metadata=self.metadata.copy()
        )

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        coords = list(self.polygon.exterior.coords)
        return {
            'id': self.id,
            'width': self.width,
            'height': self.height,
            'polygon': {'coordinates': coords},
            'position': {'x': self.position.x, 'y': self.position.y},
            'rotation': self.rotation,
            'is_remainder': self.is_remainder,
            'parent_sheet_id': self.parent_sheet_id,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Sheet':
        """Create Sheet from dictionary."""
        coords = data['polygon']['coordinates']
        polygon = Polygon(coords)
        position = Point(data['position']['x'], data['position']['y'])

        return cls(
            id=data['id'],
            width=data.get('width', 0.0),
            height=data.get('height', 0.0),
            polygon=polygon,
            position=position,
            rotation=data.get('rotation', 0.0),
            is_remainder=data.get('is_remainder', False),
            parent_sheet_id=data.get('parent_sheet_id'),
            metadata=data.get('metadata', {})
        )


@dataclass
class Cut:
    """Represents a cutting operation with metadata."""
    id: str = field(default_factory=lambda: str(uuid.uuid4()))
    template_id: str = ""
    sheet_id: str = ""
    cut_polygon: Optional[Polygon] = None
    cut_lines: List[LineSegment] = field(default_factory=list)
    timestamp: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)

    def __post_init__(self):
        """Validate cut data."""
        if not self.template_id:
            raise ValueError("Cut must have a template_id")
        if not self.sheet_id:
            raise ValueError("Cut must have a sheet_id")
        if self.cut_polygon is None:
            raise ValueError("Cut must have a cut_polygon")

        # Generate timestamp if not provided
        if self.timestamp is None:
            from datetime import datetime
            self.timestamp = datetime.now().isoformat()

    @property
    def total_cut_length(self) -> float:
        """Get total length of all cut lines."""
        return sum(line.length for line in self.cut_lines)

    @property
    def total_cut_length_cm(self) -> float:
        """Get total cut length in centimeters."""
        return self.total_cut_length / 10.0

    @property
    def cut_area(self) -> float:
        """Get the area of the cut polygon."""
        return self.cut_polygon.area if self.cut_polygon else 0.0

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        cut_coords = list(self.cut_polygon.exterior.coords) if self.cut_polygon else []
        cut_lines_data = [
            {
                'start': {'x': line.start.x, 'y': line.start.y},
                'end': {'x': line.end.x, 'y': line.end.y},
                'length_cm': line.length_cm
            }
            for line in self.cut_lines
        ]

        return {
            'id': self.id,
            'template_id': self.template_id,
            'sheet_id': self.sheet_id,
            'cut_polygon': {'coordinates': cut_coords},
            'cut_lines': cut_lines_data,
            'timestamp': self.timestamp,
            'metadata': self.metadata
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'Cut':
        """Create Cut from dictionary."""
        cut_coords = data['cut_polygon']['coordinates']
        cut_polygon = Polygon(cut_coords) if cut_coords else None

        cut_lines = []
        for line_data in data.get('cut_lines', []):
            start = Point(line_data['start']['x'], line_data['start']['y'])
            end = Point(line_data['end']['x'], line_data['end']['y'])
            cut_lines.append(LineSegment(start, end))

        return cls(
            id=data['id'],
            template_id=data['template_id'],
            sheet_id=data['sheet_id'],
            cut_polygon=cut_polygon,
            cut_lines=cut_lines,
            timestamp=data.get('timestamp'),
            metadata=data.get('metadata', {})
        )
