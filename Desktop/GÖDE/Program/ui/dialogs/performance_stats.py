"""
Performance statistics dialog for monitoring application performance.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import Dict, Any, Optional
import threading
import time

from core.performance import PerformanceMonitor, MemoryManager

logger = logging.getLogger(__name__)


class PerformanceStatsDialog:
    """
    Performance statistics monitoring dialog.
    
    Features:
    - Real-time performance metrics
    - Memory usage monitoring
    - Operation profiling
    - Performance history
    """
    
    def __init__(self, parent: tk.Widget, performance_monitor: PerformanceMonitor, 
                 memory_manager: MemoryManager):
        """
        Initialize performance stats dialog.
        
        Args:
            parent: Parent widget
            performance_monitor: Performance monitor instance
            memory_manager: Memory manager instance
        """
        self.parent = parent
        self.performance_monitor = performance_monitor
        self.memory_manager = memory_manager
        self.update_thread = None
        self.running = False
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Performance Statistics")
        self.dialog.geometry("800x600")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(parent)
        
        # Center dialog on parent
        self._center_dialog()
        
        # Create UI
        self.create_widgets()
        
        # Start monitoring
        self.start_monitoring()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.close)
        
        logger.info("Performance stats dialog initialized")
    
    def _center_dialog(self):
        """Center dialog on parent window."""
        try:
            # Get parent window position and size
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # Calculate center position
            dialog_width = 800
            dialog_height = 600
            x = parent_x + (parent_width - dialog_width) // 2
            y = parent_y + (parent_height - dialog_height) // 2
            
            self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
        except Exception as e:
            logger.warning(f"Failed to center dialog: {e}")
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_overview_tab()
        self.create_operations_tab()
        self.create_memory_tab()
        self.create_history_tab()
        
        # Create button frame
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Buttons
        ttk.Button(button_frame, text="Clear Statistics", 
                  command=self.clear_stats).pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="Export Data", 
                  command=self.export_data).pack(side=tk.LEFT, padx=(5, 0))
        
        ttk.Button(button_frame, text="Close", 
                  command=self.close).pack(side=tk.RIGHT)
    
    def create_overview_tab(self):
        """Create overview tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Overview")
        
        # Performance summary
        summary_frame = ttk.LabelFrame(frame, text="Performance Summary")
        summary_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Create labels for summary stats
        self.total_operations_label = ttk.Label(summary_frame, text="Total Operations: 0")
        self.total_operations_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.avg_time_label = ttk.Label(summary_frame, text="Average Time: 0.0ms")
        self.avg_time_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        self.slowest_op_label = ttk.Label(summary_frame, text="Slowest Operation: None")
        self.slowest_op_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
        
        # Memory summary
        memory_frame = ttk.LabelFrame(frame, text="Memory Usage")
        memory_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.process_memory_label = ttk.Label(memory_frame, text="Process Memory: 0.0 MB")
        self.process_memory_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.cache_size_label = ttk.Label(memory_frame, text="Cache Size: 0 objects")
        self.cache_size_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        self.memory_threshold_label = ttk.Label(memory_frame, text="Memory Threshold: 500 MB")
        self.memory_threshold_label.grid(row=1, column=0, columnspan=2, sticky=tk.W, padx=5, pady=2)
    
    def create_operations_tab(self):
        """Create operations performance tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Operations")
        
        # Operations tree
        tree_frame = ttk.Frame(frame)
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create treeview
        columns = ('Operation', 'Calls', 'Avg Time (ms)', 'Min (ms)', 'Max (ms)', 'Total (ms)')
        self.operations_tree = ttk.Treeview(tree_frame, columns=columns, show='headings')
        
        # Configure columns
        for col in columns:
            self.operations_tree.heading(col, text=col)
            self.operations_tree.column(col, width=120)
        
        # Add scrollbars
        v_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.operations_tree.yview)
        h_scrollbar = ttk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.operations_tree.xview)
        self.operations_tree.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack treeview and scrollbars
        self.operations_tree.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        tree_frame.grid_rowconfigure(0, weight=1)
        tree_frame.grid_columnconfigure(0, weight=1)
    
    def create_memory_tab(self):
        """Create memory monitoring tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Memory")
        
        # Memory stats
        stats_frame = ttk.LabelFrame(frame, text="Memory Statistics")
        stats_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.system_memory_label = ttk.Label(stats_frame, text="System Memory: 0 / 0 MB")
        self.system_memory_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.available_memory_label = ttk.Label(stats_frame, text="Available Memory: 0 MB")
        self.available_memory_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Cache statistics
        cache_frame = ttk.LabelFrame(frame, text="Object Cache")
        cache_frame.pack(fill=tk.X, padx=5, pady=5)
        
        self.templates_cached_label = ttk.Label(cache_frame, text="Templates Cached: 0")
        self.templates_cached_label.grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        
        self.sheets_cached_label = ttk.Label(cache_frame, text="Sheets Cached: 0")
        self.sheets_cached_label.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        self.cuts_cached_label = ttk.Label(cache_frame, text="Cuts Cached: 0")
        self.cuts_cached_label.grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        
        # Memory actions
        actions_frame = ttk.LabelFrame(frame, text="Memory Actions")
        actions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Button(actions_frame, text="Force Garbage Collection", 
                  command=self.force_gc).pack(side=tk.LEFT, padx=5, pady=5)
        
        ttk.Button(actions_frame, text="Clear All Caches", 
                  command=self.clear_caches).pack(side=tk.LEFT, padx=5, pady=5)
    
    def create_history_tab(self):
        """Create performance history tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="History")
        
        # Recent operations
        recent_frame = ttk.LabelFrame(frame, text="Recent Operations")
        recent_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Create listbox for recent operations
        listbox_frame = ttk.Frame(recent_frame)
        listbox_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        self.history_listbox = tk.Listbox(listbox_frame)
        history_scrollbar = ttk.Scrollbar(listbox_frame, orient=tk.VERTICAL, command=self.history_listbox.yview)
        self.history_listbox.configure(yscrollcommand=history_scrollbar.set)
        
        self.history_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        history_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def start_monitoring(self):
        """Start real-time monitoring."""
        self.running = True
        self.update_thread = threading.Thread(target=self._update_loop, daemon=True)
        self.update_thread.start()
    
    def stop_monitoring(self):
        """Stop real-time monitoring."""
        self.running = False
        if self.update_thread:
            self.update_thread.join(timeout=1.0)
    
    def _update_loop(self):
        """Background update loop."""
        while self.running:
            try:
                # Schedule UI update on main thread
                self.dialog.after(0, self.update_display)
                time.sleep(1.0)  # Update every second
            except Exception as e:
                logger.warning(f"Performance monitoring update failed: {e}")
                break
    
    def update_display(self):
        """Update the display with current statistics."""
        try:
            # Update overview
            self._update_overview()
            
            # Update operations table
            self._update_operations()
            
            # Update memory stats
            self._update_memory()
            
            # Update history
            self._update_history()
            
        except Exception as e:
            logger.warning(f"Failed to update performance display: {e}")
    
    def _update_overview(self):
        """Update overview tab."""
        try:
            summary = self.performance_monitor.get_performance_summary()
            
            if 'summary' in summary:
                s = summary['summary']
                self.total_operations_label.config(text=f"Total Operations: {s.get('total_operations', 0)}")
                self.avg_time_label.config(text=f"Average Time: {s.get('average_time_per_call_ms', 0):.2f}ms")
            
            if 'extremes' in summary:
                e = summary['extremes']
                slowest = e.get('slowest_operation', {})
                self.slowest_op_label.config(text=f"Slowest Operation: {slowest.get('name', 'None')} ({slowest.get('average_duration_ms', 0):.2f}ms)")
            
            # Update memory overview
            memory_stats = self.memory_manager.get_memory_stats()
            self.process_memory_label.config(text=f"Process Memory: {memory_stats.process_memory_mb:.1f} MB")
            self.cache_size_label.config(text=f"Cache Size: {memory_stats.cache_size} objects")
            
        except Exception as e:
            logger.warning(f"Failed to update overview: {e}")
    
    def _update_operations(self):
        """Update operations table."""
        try:
            # Clear existing items
            for item in self.operations_tree.get_children():
                self.operations_tree.delete(item)
            
            # Get all stats
            all_stats = self.performance_monitor.get_all_stats()
            
            # Sort by average duration (slowest first)
            sorted_stats = sorted(all_stats.values(), 
                                key=lambda s: s.average_duration_ms, 
                                reverse=True)
            
            # Add to tree
            for stat in sorted_stats:
                self.operations_tree.insert('', 'end', values=(
                    stat.operation_name,
                    stat.total_calls,
                    f"{stat.average_duration_ms:.2f}",
                    f"{stat.min_duration_ms:.2f}",
                    f"{stat.max_duration_ms:.2f}",
                    f"{stat.total_duration_ms:.2f}"
                ))
                
        except Exception as e:
            logger.warning(f"Failed to update operations table: {e}")
    
    def _update_memory(self):
        """Update memory tab."""
        try:
            memory_stats = self.memory_manager.get_memory_stats()
            
            self.system_memory_label.config(
                text=f"System Memory: {memory_stats.used_memory_mb:.0f} / {memory_stats.total_memory_mb:.0f} MB"
            )
            self.available_memory_label.config(text=f"Available Memory: {memory_stats.available_memory_mb:.0f} MB")
            
            self.templates_cached_label.config(text=f"Templates Cached: {memory_stats.templates_count}")
            self.sheets_cached_label.config(text=f"Sheets Cached: {memory_stats.sheets_count}")
            self.cuts_cached_label.config(text=f"Cuts Cached: {memory_stats.cuts_count}")
            
        except Exception as e:
            logger.warning(f"Failed to update memory stats: {e}")
    
    def _update_history(self):
        """Update history tab."""
        try:
            # Get recent operations (limit to last 50)
            all_stats = self.performance_monitor.get_all_stats()
            
            # Clear listbox
            self.history_listbox.delete(0, tk.END)
            
            # Add recent operations
            for name, stat in all_stats.items():
                recent_metrics = self.performance_monitor.get_recent_metrics(name, 5)
                for metric in recent_metrics[-5:]:  # Last 5 operations
                    timestamp = time.strftime('%H:%M:%S', time.localtime(metric.timestamp))
                    self.history_listbox.insert(tk.END, 
                        f"{timestamp} - {name}: {metric.duration_ms:.2f}ms")
            
            # Auto-scroll to bottom
            self.history_listbox.see(tk.END)
            
        except Exception as e:
            logger.warning(f"Failed to update history: {e}")
    
    def clear_stats(self):
        """Clear all performance statistics."""
        try:
            self.performance_monitor.clear_metrics()
            self.update_display()
            logger.info("Performance statistics cleared")
        except Exception as e:
            logger.warning(f"Failed to clear statistics: {e}")
    
    def export_data(self):
        """Export performance data."""
        try:
            from tkinter import filedialog
            
            file_path = filedialog.asksaveasfilename(
                title="Export Performance Data",
                defaultextension=".json",
                filetypes=[("JSON files", "*.json"), ("All files", "*.*")]
            )
            
            if file_path:
                import json
                
                data = {
                    'performance_summary': self.performance_monitor.get_performance_summary(),
                    'all_stats': {name: {
                        'operation_name': stat.operation_name,
                        'total_calls': stat.total_calls,
                        'average_duration_ms': stat.average_duration_ms,
                        'min_duration_ms': stat.min_duration_ms,
                        'max_duration_ms': stat.max_duration_ms,
                        'total_duration_ms': stat.total_duration_ms
                    } for name, stat in self.performance_monitor.get_all_stats().items()},
                    'memory_stats': {
                        'process_memory_mb': self.memory_manager.get_memory_stats().process_memory_mb,
                        'cache_size': self.memory_manager.get_memory_stats().cache_size,
                        'cache_statistics': self.memory_manager.get_cache_statistics()
                    }
                }
                
                with open(file_path, 'w') as f:
                    json.dump(data, f, indent=2)
                
                logger.info(f"Performance data exported to {file_path}")
                
        except Exception as e:
            logger.warning(f"Failed to export performance data: {e}")
    
    def force_gc(self):
        """Force garbage collection."""
        try:
            collected = self.memory_manager.force_garbage_collection()
            logger.info(f"Garbage collection freed {collected} objects")
        except Exception as e:
            logger.warning(f"Failed to force garbage collection: {e}")
    
    def clear_caches(self):
        """Clear all object caches."""
        try:
            self.memory_manager.clear_all_caches()
            logger.info("All caches cleared")
        except Exception as e:
            logger.warning(f"Failed to clear caches: {e}")
    
    def close(self):
        """Close the dialog."""
        self.stop_monitoring()
        self.dialog.destroy()
