"""
DXF import dialog for configuring import settings.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox
import logging
from pathlib import Path
from typing import Optional, Dict, Any

from utils.validation import validate_file_path, validate_numeric_input
from core.dxf_import.converter import DXFConverter

logger = logging.getLogger(__name__)


class ImportDXFDialog:
    """Dialog for importing DXF files with configuration options."""
    
    def __init__(self, parent: tk.Widget, main_window):
        """
        Initialize import dialog.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        self.parent = parent
        self.main_window = main_window
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Import DXF File")
        self.dialog.geometry("500x400")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Initialize converter
        self.converter = DXFConverter()
        
        # Dialog state
        self.file_path = ""
        self.selected_layer = None
        self.units = "mm"
        self.scale_factor = 1.0
        
        # Create UI
        self.create_widgets()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def center_dialog(self):
        """Center the dialog on parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets."""
        # File selection section
        file_frame = ttk.LabelFrame(self.dialog, text="File Selection")
        file_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # File path entry
        path_frame = ttk.Frame(file_frame)
        path_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(path_frame, text="DXF File:").pack(anchor=tk.W)
        
        file_entry_frame = ttk.Frame(path_frame)
        file_entry_frame.pack(fill=tk.X, pady=2)
        
        self.file_var = tk.StringVar()
        self.file_entry = ttk.Entry(file_entry_frame, textvariable=self.file_var, state='readonly')
        self.file_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)
        
        ttk.Button(
            file_entry_frame,
            text="Browse...",
            command=self.browse_file
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        # File info display
        self.info_text = tk.Text(
            file_frame,
            height=4,
            wrap=tk.WORD,
            state=tk.DISABLED
        )
        self.info_text.pack(fill=tk.X, padx=5, pady=5)
        
        # Import settings section
        settings_frame = ttk.LabelFrame(self.dialog, text="Import Settings")
        settings_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Layer selection
        layer_frame = ttk.Frame(settings_frame)
        layer_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(layer_frame, text="Layer:").pack(anchor=tk.W)
        
        self.layer_var = tk.StringVar(value="All Layers")
        self.layer_combo = ttk.Combobox(
            layer_frame,
            textvariable=self.layer_var,
            state='readonly'
        )
        self.layer_combo.pack(fill=tk.X, pady=2)
        
        # Units selection
        units_frame = ttk.Frame(settings_frame)
        units_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(units_frame, text="Units:").pack(anchor=tk.W)
        
        self.units_var = tk.StringVar(value="mm")
        units_combo = ttk.Combobox(
            units_frame,
            textvariable=self.units_var,
            values=['mm', 'cm', 'm', 'inch', 'ft'],
            state='readonly'
        )
        units_combo.pack(fill=tk.X, pady=2)
        
        # Scale factor
        scale_frame = ttk.Frame(settings_frame)
        scale_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(scale_frame, text="Scale Factor:").pack(anchor=tk.W)
        
        self.scale_var = tk.StringVar(value="1.0")
        scale_entry = ttk.Entry(scale_frame, textvariable=self.scale_var)
        scale_entry.pack(fill=tk.X, pady=2)
        
        # Buttons
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=10)
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(
            button_frame,
            text="Import",
            command=self.import_file
        ).pack(side=tk.RIGHT)
        
        # Bind events
        self.file_var.trace('w', self.on_file_change)
    
    def browse_file(self):
        """Browse for DXF file."""
        file_path = filedialog.askopenfilename(
            parent=self.dialog,
            title="Select DXF File",
            filetypes=[
                ("DXF files", "*.dxf"),
                ("All files", "*.*")
            ]
        )
        
        if file_path:
            self.file_var.set(file_path)
    
    def on_file_change(self, *args):
        """Handle file path change."""
        file_path = self.file_var.get()
        
        if file_path and validate_file_path(file_path, must_exist=True, allowed_extensions=['.dxf']):
            self.load_file_info(file_path)
        else:
            self.clear_file_info()
    
    def load_file_info(self, file_path: str):
        """
        Load and display file information.
        
        Args:
            file_path: Path to DXF file
        """
        try:
            # Get file info
            file_info = self.converter.get_file_info(file_path)
            
            # Update info display
            self.info_text.config(state=tk.NORMAL)
            self.info_text.delete(1.0, tk.END)
            
            info_lines = [
                f"File: {file_info.get('file_name', 'Unknown')}",
                f"DXF Version: {file_info.get('dxf_version', 'Unknown')}",
                f"Estimated Templates: {file_info.get('estimated_templates', 0)}",
                f"Layers: {len(file_info.get('layers', []))}"
            ]
            
            self.info_text.insert(tk.END, '\n'.join(info_lines))
            self.info_text.config(state=tk.DISABLED)
            
            # Update layer combo
            layers = ['All Layers'] + [layer['name'] for layer in file_info.get('layers', [])]
            self.layer_combo['values'] = layers
            self.layer_combo.set('All Layers')
            
        except Exception as e:
            logger.error(f"Failed to load file info: {e}")
            self.clear_file_info()
            messagebox.showerror("Error", f"Failed to read DXF file:\n{str(e)}")
    
    def clear_file_info(self):
        """Clear file information display."""
        self.info_text.config(state=tk.NORMAL)
        self.info_text.delete(1.0, tk.END)
        self.info_text.config(state=tk.DISABLED)
        
        self.layer_combo['values'] = ['All Layers']
        self.layer_combo.set('All Layers')
    
    def validate_settings(self) -> bool:
        """
        Validate import settings.
        
        Returns:
            True if settings are valid
        """
        # Validate file path
        file_path = self.file_var.get()
        if not file_path:
            messagebox.showerror("Error", "Please select a DXF file.")
            return False
        
        if not validate_file_path(file_path, must_exist=True, allowed_extensions=['.dxf']):
            messagebox.showerror("Error", "Invalid DXF file.")
            return False
        
        # Validate scale factor
        scale_text = self.scale_var.get()
        is_valid, scale_value = validate_numeric_input(scale_text, min_value=0.001, max_value=1000.0)
        
        if not is_valid:
            messagebox.showerror("Error", "Scale factor must be a number between 0.001 and 1000.")
            return False
        
        return True
    
    def import_file(self):
        """Import the selected DXF file."""
        if not self.validate_settings():
            return
        
        try:
            # Get settings
            file_path = self.file_var.get()
            layer_name = self.layer_var.get() if self.layer_var.get() != "All Layers" else None
            units = self.units_var.get()
            scale_factor = float(self.scale_var.get())
            
            # Import file
            self.result = self.converter.import_dxf(
                file_path=file_path,
                layer_name=layer_name,
                units=units,
                scale_factor=scale_factor
            )
            
            # Show success message
            template_count = len(self.result.get('templates', []))
            messagebox.showinfo(
                "Import Successful",
                f"Successfully imported {template_count} templates from DXF file."
            )
            
            # Close dialog
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"Import failed: {e}")
            messagebox.showerror("Import Error", f"Failed to import DXF file:\n{str(e)}")
    
    def cancel(self):
        """Cancel the import."""
        self.result = None
        self.dialog.destroy()
