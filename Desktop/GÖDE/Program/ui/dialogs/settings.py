"""
Advanced settings dialog for the drywall optimization application.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Dict, Any, Optional
import json
from pathlib import Path

logger = logging.getLogger(__name__)


class SettingsDialog:
    """
    Advanced settings configuration dialog.
    
    Features:
    - Performance settings
    - Canvas configuration
    - Export options
    - Memory management
    - Cutting parameters
    """
    
    def __init__(self, parent: tk.Widget, current_config: Dict[str, Any]):
        """
        Initialize settings dialog.
        
        Args:
            parent: Parent widget
            current_config: Current application configuration
        """
        self.parent = parent
        self.current_config = current_config.copy()
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Advanced Settings")
        self.dialog.geometry("600x500")
        self.dialog.resizable(True, True)
        
        # Make dialog modal
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog on parent
        self._center_dialog()
        
        # Create UI
        self.create_widgets()
        
        # Load current settings
        self.load_settings()
        
        # Handle window close
        self.dialog.protocol("WM_DELETE_WINDOW", self.cancel)
        
        logger.info("Settings dialog initialized")
    
    def _center_dialog(self):
        """Center dialog on parent window."""
        try:
            # Get parent window position and size
            parent_x = self.parent.winfo_rootx()
            parent_y = self.parent.winfo_rooty()
            parent_width = self.parent.winfo_width()
            parent_height = self.parent.winfo_height()
            
            # Calculate center position
            dialog_width = 600
            dialog_height = 500
            x = parent_x + (parent_width - dialog_width) // 2
            y = parent_y + (parent_height - dialog_height) // 2
            
            self.dialog.geometry(f"{dialog_width}x{dialog_height}+{x}+{y}")
            
        except Exception as e:
            logger.warning(f"Failed to center dialog: {e}")
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.dialog)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_performance_tab()
        self.create_canvas_tab()
        self.create_cutting_tab()
        self.create_export_tab()
        self.create_memory_tab()
        
        # Create button frame
        button_frame = ttk.Frame(self.dialog)
        button_frame.pack(fill=tk.X, padx=10, pady=(0, 10))
        
        # Buttons
        ttk.Button(button_frame, text="Reset to Defaults", 
                  command=self.reset_defaults).pack(side=tk.LEFT)
        
        ttk.Button(button_frame, text="Cancel", 
                  command=self.cancel).pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(button_frame, text="Apply", 
                  command=self.apply).pack(side=tk.RIGHT)
    
    def create_performance_tab(self):
        """Create performance settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Performance")
        
        # Spatial indexing
        spatial_frame = ttk.LabelFrame(frame, text="Spatial Indexing")
        spatial_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(spatial_frame, text="Enable spatial indexing:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.spatial_enabled = tk.BooleanVar(value=True)
        ttk.Checkbutton(spatial_frame, variable=self.spatial_enabled).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Rendering optimization
        render_frame = ttk.LabelFrame(frame, text="Rendering")
        render_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(render_frame, text="Enable render optimization:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.render_optimization = tk.BooleanVar(value=True)
        ttk.Checkbutton(render_frame, variable=self.render_optimization).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(render_frame, text="Max objects on canvas:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_objects = tk.IntVar(value=1000)
        ttk.Spinbox(render_frame, from_=100, to=10000, textvariable=self.max_objects, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(render_frame, text="LOD threshold:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.lod_threshold = tk.DoubleVar(value=0.1)
        ttk.Spinbox(render_frame, from_=0.01, to=1.0, increment=0.01, textvariable=self.lod_threshold, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
    
    def create_canvas_tab(self):
        """Create canvas settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Canvas")
        
        # Grid settings
        grid_frame = ttk.LabelFrame(frame, text="Grid")
        grid_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(grid_frame, text="Show grid:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.show_grid = tk.BooleanVar(value=True)
        ttk.Checkbutton(grid_frame, variable=self.show_grid).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(grid_frame, text="Grid spacing (mm):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.grid_spacing = tk.IntVar(value=50)
        ttk.Spinbox(grid_frame, from_=10, to=500, textvariable=self.grid_spacing, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(grid_frame, text="Grid color:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.grid_color = tk.StringVar(value="#d0d0d0")
        ttk.Entry(grid_frame, textvariable=self.grid_color, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Zoom settings
        zoom_frame = ttk.LabelFrame(frame, text="Zoom")
        zoom_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(zoom_frame, text="Min zoom:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.min_zoom = tk.DoubleVar(value=0.1)
        ttk.Spinbox(zoom_frame, from_=0.01, to=1.0, increment=0.01, textvariable=self.min_zoom, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(zoom_frame, text="Max zoom:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_zoom = tk.DoubleVar(value=10.0)
        ttk.Spinbox(zoom_frame, from_=1.0, to=50.0, increment=1.0, textvariable=self.max_zoom, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(zoom_frame, text="Zoom step:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.zoom_step = tk.DoubleVar(value=0.1)
        ttk.Spinbox(zoom_frame, from_=0.01, to=1.0, increment=0.01, textvariable=self.zoom_step, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Snapping settings
        snap_frame = ttk.LabelFrame(frame, text="Snapping")
        snap_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(snap_frame, text="Default tolerance (mm):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.snap_tolerance = tk.DoubleVar(value=5.0)
        ttk.Spinbox(snap_frame, from_=0.1, to=50.0, increment=0.1, textvariable=self.snap_tolerance, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(snap_frame, text="Snap to grid:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.snap_to_grid = tk.BooleanVar(value=True)
        ttk.Checkbutton(snap_frame, variable=self.snap_to_grid).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(snap_frame, text="Snap to objects:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.snap_to_objects = tk.BooleanVar(value=True)
        ttk.Checkbutton(snap_frame, variable=self.snap_to_objects).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
    
    def create_cutting_tab(self):
        """Create cutting settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Cutting")
        
        # Precision settings
        precision_frame = ttk.LabelFrame(frame, text="Precision")
        precision_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(precision_frame, text="Precision (mm):").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.cutting_precision = tk.DoubleVar(value=0.1)
        ttk.Spinbox(precision_frame, from_=0.01, to=1.0, increment=0.01, textvariable=self.cutting_precision, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(precision_frame, text="Auto cleanup small pieces:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.auto_cleanup = tk.BooleanVar(value=True)
        ttk.Checkbutton(precision_frame, variable=self.auto_cleanup).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(precision_frame, text="Min piece area (mm²):").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.min_piece_area = tk.IntVar(value=10000)
        ttk.Spinbox(precision_frame, from_=1000, to=100000, increment=1000, textvariable=self.min_piece_area, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
    
    def create_export_tab(self):
        """Create export settings tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Export")
        
        # PDF settings
        pdf_frame = ttk.LabelFrame(frame, text="PDF Export")
        pdf_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(pdf_frame, text="Page size:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.page_size = tk.StringVar(value="A4")
        page_combo = ttk.Combobox(pdf_frame, textvariable=self.page_size, values=["A4", "A3", "Letter"], width=10)
        page_combo.grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(pdf_frame, text="Margin (mm):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.pdf_margin = tk.IntVar(value=20)
        ttk.Spinbox(pdf_frame, from_=5, to=50, textvariable=self.pdf_margin, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(pdf_frame, text="Show dimensions:").grid(row=2, column=0, sticky=tk.W, padx=5, pady=2)
        self.show_dimensions = tk.BooleanVar(value=True)
        ttk.Checkbutton(pdf_frame, variable=self.show_dimensions).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(pdf_frame, text="Show cut IDs:").grid(row=3, column=0, sticky=tk.W, padx=5, pady=2)
        self.show_cut_ids = tk.BooleanVar(value=True)
        ttk.Checkbutton(pdf_frame, variable=self.show_cut_ids).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)
    
    def create_memory_tab(self):
        """Create memory management tab."""
        frame = ttk.Frame(self.notebook)
        self.notebook.add(frame, text="Memory")
        
        # Cache settings
        cache_frame = ttk.LabelFrame(frame, text="Object Caching")
        cache_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(cache_frame, text="Max cache size:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_cache_size = tk.IntVar(value=1000)
        ttk.Spinbox(cache_frame, from_=100, to=10000, increment=100, textvariable=self.max_cache_size, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(cache_frame, text="Memory threshold (MB):").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.memory_threshold = tk.IntVar(value=500)
        ttk.Spinbox(cache_frame, from_=100, to=2000, increment=50, textvariable=self.memory_threshold, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Backup settings
        backup_frame = ttk.LabelFrame(frame, text="Project Backups")
        backup_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(backup_frame, text="Create backups:").grid(row=0, column=0, sticky=tk.W, padx=5, pady=2)
        self.create_backups = tk.BooleanVar(value=True)
        ttk.Checkbutton(backup_frame, variable=self.create_backups).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(backup_frame, text="Max backups:").grid(row=1, column=0, sticky=tk.W, padx=5, pady=2)
        self.max_backups = tk.IntVar(value=5)
        ttk.Spinbox(backup_frame, from_=1, to=20, textvariable=self.max_backups, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
    
    def load_settings(self):
        """Load current settings into dialog."""
        try:
            # Performance settings
            perf_config = self.current_config.get('performance', {})
            self.spatial_enabled.set(perf_config.get('spatial_index_enabled', True))
            self.render_optimization.set(perf_config.get('render_optimization', True))
            self.max_objects.set(perf_config.get('max_objects_on_canvas', 1000))
            
            # Canvas settings
            canvas_config = self.current_config.get('canvas', {})
            self.show_grid.set(canvas_config.get('show_grid', True))
            self.grid_spacing.set(canvas_config.get('grid_spacing', 50))
            self.grid_color.set(canvas_config.get('grid_color', '#d0d0d0'))
            
            zoom_config = canvas_config.get('zoom', {})
            self.min_zoom.set(zoom_config.get('min', 0.1))
            self.max_zoom.set(zoom_config.get('max', 10.0))
            self.zoom_step.set(zoom_config.get('step', 0.1))
            
            # Snapping settings
            snap_config = self.current_config.get('snapping', {})
            self.snap_tolerance.set(snap_config.get('default_tolerance_mm', 5.0))
            self.snap_to_grid.set(snap_config.get('snap_to_grid', True))
            self.snap_to_objects.set(snap_config.get('snap_to_objects', True))
            
            # Cutting settings
            cutting_config = self.current_config.get('cutting', {})
            self.cutting_precision.set(cutting_config.get('precision_mm', 0.1))
            self.auto_cleanup.set(cutting_config.get('auto_cleanup_small_pieces', True))
            self.min_piece_area.set(cutting_config.get('min_piece_area_mm2', 10000))
            
            # Export settings
            export_config = self.current_config.get('export', {})
            pdf_config = export_config.get('pdf', {})
            self.page_size.set(pdf_config.get('page_size', 'A4'))
            self.pdf_margin.set(pdf_config.get('margin_mm', 20))
            self.show_dimensions.set(pdf_config.get('show_dimensions', True))
            self.show_cut_ids.set(pdf_config.get('show_cut_ids', True))
            
            # Memory settings
            self.max_cache_size.set(1000)  # Default values
            self.memory_threshold.set(500)
            self.create_backups.set(True)
            self.max_backups.set(5)
            
        except Exception as e:
            logger.warning(f"Failed to load some settings: {e}")
    
    def get_settings(self) -> Dict[str, Any]:
        """Get current settings from dialog."""
        return {
            'performance': {
                'spatial_index_enabled': self.spatial_enabled.get(),
                'render_optimization': self.render_optimization.get(),
                'max_objects_on_canvas': self.max_objects.get()
            },
            'canvas': {
                'show_grid': self.show_grid.get(),
                'grid_spacing': self.grid_spacing.get(),
                'grid_color': self.grid_color.get(),
                'zoom': {
                    'min': self.min_zoom.get(),
                    'max': self.max_zoom.get(),
                    'step': self.zoom_step.get()
                }
            },
            'snapping': {
                'default_tolerance_mm': self.snap_tolerance.get(),
                'snap_to_grid': self.snap_to_grid.get(),
                'snap_to_objects': self.snap_to_objects.get()
            },
            'cutting': {
                'precision_mm': self.cutting_precision.get(),
                'auto_cleanup_small_pieces': self.auto_cleanup.get(),
                'min_piece_area_mm2': self.min_piece_area.get()
            },
            'export': {
                'pdf': {
                    'page_size': self.page_size.get(),
                    'margin_mm': self.pdf_margin.get(),
                    'show_dimensions': self.show_dimensions.get(),
                    'show_cut_ids': self.show_cut_ids.get()
                }
            },
            'memory': {
                'max_cache_size': self.max_cache_size.get(),
                'memory_threshold_mb': self.memory_threshold.get(),
                'create_backups': self.create_backups.get(),
                'max_backups': self.max_backups.get()
            }
        }
    
    def reset_defaults(self):
        """Reset all settings to defaults."""
        if messagebox.askyesno("Reset Settings", "Reset all settings to default values?"):
            # Reset to default values
            self.spatial_enabled.set(True)
            self.render_optimization.set(True)
            self.max_objects.set(1000)
            
            self.show_grid.set(True)
            self.grid_spacing.set(50)
            self.grid_color.set('#d0d0d0')
            self.min_zoom.set(0.1)
            self.max_zoom.set(10.0)
            self.zoom_step.set(0.1)
            
            self.snap_tolerance.set(5.0)
            self.snap_to_grid.set(True)
            self.snap_to_objects.set(True)
            
            self.cutting_precision.set(0.1)
            self.auto_cleanup.set(True)
            self.min_piece_area.set(10000)
            
            self.page_size.set('A4')
            self.pdf_margin.set(20)
            self.show_dimensions.set(True)
            self.show_cut_ids.set(True)
            
            self.max_cache_size.set(1000)
            self.memory_threshold.set(500)
            self.create_backups.set(True)
            self.max_backups.set(5)
    
    def apply(self):
        """Apply settings and close dialog."""
        try:
            self.result = self.get_settings()
            self.dialog.destroy()
        except Exception as e:
            messagebox.showerror("Error", f"Failed to apply settings: {str(e)}")
    
    def cancel(self):
        """Cancel dialog without applying changes."""
        self.result = None
        self.dialog.destroy()
