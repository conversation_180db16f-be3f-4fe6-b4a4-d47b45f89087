"""
Add sheet dialog for creating new sheet types.
"""

import tkinter as tk
from tkinter import ttk, messagebox
import logging
from typing import Optional, Dict, Any

from utils.validation import validate_sheet_dimensions, validate_quantity

logger = logging.getLogger(__name__)


class AddSheetDialog:
    """Dialog for adding new sheet types to inventory."""
    
    def __init__(self, parent: tk.Widget, main_window):
        """
        Initialize add sheet dialog.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        self.parent = parent
        self.main_window = main_window
        self.result = None
        
        # Create dialog window
        self.dialog = tk.Toplevel(parent)
        self.dialog.title("Add Sheet Type")
        self.dialog.geometry("350x300")
        self.dialog.resizable(False, False)
        self.dialog.transient(parent)
        self.dialog.grab_set()
        
        # Center dialog
        self.center_dialog()
        
        # Create UI
        self.create_widgets()
        
        # Focus on first entry
        self.width_entry.focus_set()
        
        # Wait for dialog to close
        self.dialog.wait_window()
    
    def center_dialog(self):
        """Center the dialog on parent window."""
        self.dialog.update_idletasks()
        
        # Get parent window position and size
        parent_x = self.parent.winfo_rootx()
        parent_y = self.parent.winfo_rooty()
        parent_width = self.parent.winfo_width()
        parent_height = self.parent.winfo_height()
        
        # Calculate dialog position
        dialog_width = self.dialog.winfo_width()
        dialog_height = self.dialog.winfo_height()
        
        x = parent_x + (parent_width - dialog_width) // 2
        y = parent_y + (parent_height - dialog_height) // 2
        
        self.dialog.geometry(f"+{x}+{y}")
    
    def create_widgets(self):
        """Create dialog widgets."""
        # Main frame
        main_frame = ttk.Frame(self.dialog)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Dimensions section
        dims_frame = ttk.LabelFrame(main_frame, text="Sheet Dimensions")
        dims_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Width
        width_frame = ttk.Frame(dims_frame)
        width_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(width_frame, text="Width:").pack(side=tk.LEFT)
        
        self.width_var = tk.StringVar()
        self.width_entry = ttk.Entry(width_frame, textvariable=self.width_var, width=10)
        self.width_entry.pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Label(width_frame, text="mm").pack(side=tk.RIGHT)
        
        # Height
        height_frame = ttk.Frame(dims_frame)
        height_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(height_frame, text="Height:").pack(side=tk.LEFT)
        
        self.height_var = tk.StringVar()
        self.height_entry = ttk.Entry(height_frame, textvariable=self.height_var, width=10)
        self.height_entry.pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Label(height_frame, text="mm").pack(side=tk.RIGHT)
        
        # Quantity section
        qty_frame = ttk.LabelFrame(main_frame, text="Quantity")
        qty_frame.pack(fill=tk.X, pady=(0, 10))
        
        qty_input_frame = ttk.Frame(qty_frame)
        qty_input_frame.pack(fill=tk.X, padx=5, pady=5)
        
        ttk.Label(qty_input_frame, text="Quantity:").pack(side=tk.LEFT)
        
        self.quantity_var = tk.StringVar(value="1")
        self.quantity_entry = ttk.Entry(qty_input_frame, textvariable=self.quantity_var, width=10)
        self.quantity_entry.pack(side=tk.RIGHT)
        
        # Presets section
        presets_frame = ttk.LabelFrame(main_frame, text="Common Sizes")
        presets_frame.pack(fill=tk.X, pady=(0, 10))
        
        # Create preset buttons
        preset_sizes = [
            ("1200 × 2400", 1200, 2400),
            ("1200 × 3000", 1200, 3000),
            ("900 × 2400", 900, 2400),
            ("600 × 2400", 600, 2400)
        ]
        
        for i, (label, width, height) in enumerate(preset_sizes):
            row = i // 2
            col = i % 2
            
            btn = ttk.Button(
                presets_frame,
                text=label,
                command=lambda w=width, h=height: self.set_preset(w, h)
            )
            btn.grid(row=row, column=col, padx=2, pady=2, sticky='ew')
        
        # Configure grid weights
        presets_frame.grid_columnconfigure(0, weight=1)
        presets_frame.grid_columnconfigure(1, weight=1)
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(10, 0))
        
        ttk.Button(
            button_frame,
            text="Cancel",
            command=self.cancel
        ).pack(side=tk.RIGHT, padx=(5, 0))
        
        ttk.Button(
            button_frame,
            text="Add",
            command=self.add_sheet
        ).pack(side=tk.RIGHT)
        
        # Bind events
        self.width_entry.bind('<Return>', lambda e: self.height_entry.focus_set())
        self.height_entry.bind('<Return>', lambda e: self.quantity_entry.focus_set())
        self.quantity_entry.bind('<Return>', lambda e: self.add_sheet())
        
        # Bind validation
        self.width_var.trace('w', self.on_dimension_change)
        self.height_var.trace('w', self.on_dimension_change)
    
    def set_preset(self, width: float, height: float):
        """
        Set preset dimensions.
        
        Args:
            width: Preset width
            height: Preset height
        """
        self.width_var.set(str(width))
        self.height_var.set(str(height))
        self.quantity_entry.focus_set()
    
    def on_dimension_change(self, *args):
        """Handle dimension change for validation feedback."""
        # Could add real-time validation feedback here
        pass
    
    def validate_input(self) -> bool:
        """
        Validate user input.
        
        Returns:
            True if input is valid
        """
        # Validate dimensions
        width_text = self.width_var.get()
        height_text = self.height_var.get()
        
        if not width_text or not height_text:
            messagebox.showerror("Error", "Please enter both width and height.")
            return False
        
        is_valid, width, height = validate_sheet_dimensions(width_text, height_text)
        if not is_valid:
            messagebox.showerror(
                "Error", 
                "Invalid dimensions. Width and height must be numbers between 1 and 10000 mm."
            )
            return False
        
        # Validate quantity
        quantity_text = self.quantity_var.get()
        is_valid, quantity = validate_quantity(quantity_text)
        if not is_valid:
            messagebox.showerror(
                "Error",
                "Invalid quantity. Must be a whole number between 0 and 10000."
            )
            return False
        
        return True
    
    def add_sheet(self):
        """Add the sheet type."""
        if not self.validate_input():
            return
        
        try:
            # Get validated values
            width = float(self.width_var.get())
            height = float(self.height_var.get())
            quantity = int(self.quantity_var.get())
            
            # Create result
            self.result = {
                'width': width,
                'height': height,
                'quantity': quantity
            }
            
            # Add to materials panel
            self.main_window.materials_panel.add_sheet_type_data(width, height, quantity)
            
            # Show success message
            messagebox.showinfo(
                "Success",
                f"Added {quantity} sheets of size {width} × {height} mm"
            )
            
            # Close dialog
            self.dialog.destroy()
            
        except Exception as e:
            logger.error(f"Failed to add sheet: {e}")
            messagebox.showerror("Error", f"Failed to add sheet:\n{str(e)}")
    
    def cancel(self):
        """Cancel adding sheet."""
        self.result = None
        self.dialog.destroy()
