"""
Properties panel for displaying and editing object properties.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import Optional, Any, Dict

logger = logging.getLogger(__name__)


class PropertiesPanel:
    """Panel for displaying and editing object properties."""
    
    def __init__(self, parent: tk.Widget, main_window):
        """
        Initialize properties panel.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        self.parent = parent
        self.main_window = main_window
        
        # Panel state
        self.selected_object = None
        self.snap_tolerance = 5.0
        self.snap_enabled = True
        
        # Create UI
        self.create_widgets()
        
        logger.info("Properties panel initialized")
    
    def create_widgets(self):
        """Create the panel widgets."""
        # Snap settings section
        snap_frame = ttk.LabelFrame(self.parent, text="Snap Settings")
        snap_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Snap enabled checkbox
        self.snap_var = tk.BooleanVar(value=self.snap_enabled)
        snap_check = ttk.Checkbutton(
            snap_frame,
            text="Enable Snapping",
            variable=self.snap_var,
            command=self.on_snap_toggle
        )
        snap_check.pack(anchor=tk.W, padx=5, pady=2)
        
        # Snap tolerance
        tolerance_frame = ttk.Frame(snap_frame)
        tolerance_frame.pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Label(tolerance_frame, text="Tolerance:").pack(side=tk.LEFT)
        
        self.tolerance_var = tk.StringVar(value=str(self.snap_tolerance))
        tolerance_entry = ttk.Entry(
            tolerance_frame,
            textvariable=self.tolerance_var,
            width=8
        )
        tolerance_entry.pack(side=tk.LEFT, padx=5)
        tolerance_entry.bind('<Return>', self.on_tolerance_change)
        tolerance_entry.bind('<FocusOut>', self.on_tolerance_change)
        
        ttk.Label(tolerance_frame, text="mm").pack(side=tk.LEFT)
        
        # Selection info section
        selection_frame = ttk.LabelFrame(self.parent, text="Selection")
        selection_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Selected object info
        self.selection_label = ttk.Label(
            selection_frame,
            text="No selection",
            wraplength=200
        )
        self.selection_label.pack(anchor=tk.W, padx=5, pady=2)
        
        # Object properties section
        props_frame = ttk.LabelFrame(self.parent, text="Properties")
        props_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Properties treeview
        self.props_tree = ttk.Treeview(
            props_frame,
            columns=('value',),
            show='tree headings',
            height=8
        )
        
        self.props_tree.heading('#0', text='Property')
        self.props_tree.heading('value', text='Value')
        
        self.props_tree.column('#0', width=100)
        self.props_tree.column('value', width=100)
        
        # Scrollbar for properties
        props_scrollbar = ttk.Scrollbar(props_frame, orient=tk.VERTICAL, command=self.props_tree.yview)
        self.props_tree.configure(yscrollcommand=props_scrollbar.set)
        
        # Pack properties widgets
        self.props_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        props_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Actions section
        actions_frame = ttk.LabelFrame(self.parent, text="Actions")
        actions_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Action buttons
        ttk.Button(
            actions_frame,
            text="Cut Selected",
            command=self.cut_selected
        ).pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(
            actions_frame,
            text="Delete Selected",
            command=self.delete_selected
        ).pack(fill=tk.X, padx=5, pady=2)
        
        ttk.Button(
            actions_frame,
            text="Duplicate",
            command=self.duplicate_selected
        ).pack(fill=tk.X, padx=5, pady=2)
        
        # Load initial settings
        self.load_settings()
    
    def on_snap_toggle(self):
        """Handle snap toggle."""
        self.snap_enabled = self.snap_var.get()
        self.update_snap_status()
        logger.info(f"Snapping {'enabled' if self.snap_enabled else 'disabled'}")
    
    def on_tolerance_change(self, event=None):
        """Handle tolerance value change."""
        try:
            new_tolerance = float(self.tolerance_var.get())
            if 0.1 <= new_tolerance <= 100.0:
                self.snap_tolerance = new_tolerance
                logger.info(f"Snap tolerance set to {self.snap_tolerance} mm")
            else:
                # Reset to previous value
                self.tolerance_var.set(str(self.snap_tolerance))
        except ValueError:
            # Reset to previous value
            self.tolerance_var.set(str(self.snap_tolerance))
    
    def update_snap_status(self):
        """Update snap status in main window."""
        status = "ON" if self.snap_enabled else "OFF"
        self.main_window.snap_label.config(text=f"Snap: {status}")
    
    def load_settings(self):
        """Load settings from configuration."""
        try:
            config = self.main_window.config
            snap_config = config.get('snapping', {})
            
            self.snap_tolerance = snap_config.get('default_tolerance_mm', 5.0)
            self.snap_enabled = snap_config.get('enabled', True)
            
            # Update UI
            self.tolerance_var.set(str(self.snap_tolerance))
            self.snap_var.set(self.snap_enabled)
            self.update_snap_status()
            
        except Exception as e:
            logger.warning(f"Failed to load snap settings: {e}")
    
    def set_selected_object(self, obj: Optional[Any]):
        """
        Set the currently selected object.
        
        Args:
            obj: Selected object (Template, Sheet, etc.)
        """
        self.selected_object = obj
        self.update_selection_display()
        self.update_properties_display()
    
    def update_selection_display(self):
        """Update the selection information display."""
        if self.selected_object is None:
            self.selection_label.config(text="No selection")
        else:
            # Determine object type and display info
            obj_type = type(self.selected_object).__name__
            obj_id = getattr(self.selected_object, 'id', 'Unknown')
            self.selection_label.config(text=f"{obj_type}: {obj_id}")
    
    def update_properties_display(self):
        """Update the properties tree display."""
        # Clear existing properties
        for item in self.props_tree.get_children():
            self.props_tree.delete(item)
        
        if self.selected_object is None:
            return
        
        # Add properties based on object type
        if hasattr(self.selected_object, 'polygon'):
            # Geometry properties
            geom_item = self.props_tree.insert('', 'end', text='Geometry', values=('',))
            
            area = getattr(self.selected_object, 'area', 0)
            self.props_tree.insert(geom_item, 'end', text='Area', values=(f'{area:.1f} mm²',))
            
            if hasattr(self.selected_object, 'bounds'):
                bounds = self.selected_object.bounds
                width = bounds[2] - bounds[0]
                height = bounds[3] - bounds[1]
                self.props_tree.insert(geom_item, 'end', text='Width', values=(f'{width:.1f} mm',))
                self.props_tree.insert(geom_item, 'end', text='Height', values=(f'{height:.1f} mm',))
        
        if hasattr(self.selected_object, 'position'):
            # Position properties
            pos_item = self.props_tree.insert('', 'end', text='Position', values=('',))
            pos = self.selected_object.position
            self.props_tree.insert(pos_item, 'end', text='X', values=(f'{pos.x:.1f} mm',))
            self.props_tree.insert(pos_item, 'end', text='Y', values=(f'{pos.y:.1f} mm',))
        
        if hasattr(self.selected_object, 'rotation'):
            # Rotation property
            rotation = self.selected_object.rotation
            self.props_tree.insert('', 'end', text='Rotation', values=(f'{rotation:.1f}°',))
        
        if hasattr(self.selected_object, 'metadata'):
            # Metadata properties
            metadata = self.selected_object.metadata
            if metadata:
                meta_item = self.props_tree.insert('', 'end', text='Metadata', values=('',))
                for key, value in metadata.items():
                    self.props_tree.insert(meta_item, 'end', text=key, values=(str(value),))
        
        # Expand all items
        for item in self.props_tree.get_children():
            self.props_tree.item(item, open=True)
    
    def cut_selected(self):
        """Cut the selected object."""
        self.main_window.cut_selected()
    
    def delete_selected(self):
        """Delete the selected object."""
        self.main_window.delete_selected()
    
    def duplicate_selected(self):
        """Duplicate the selected object."""
        # TODO: Implement duplication
        logger.info("Duplicate functionality not yet implemented")
    
    def get_snap_settings(self) -> Dict[str, Any]:
        """
        Get current snap settings.
        
        Returns:
            Dictionary with snap settings
        """
        return {
            'enabled': self.snap_enabled,
            'tolerance': self.snap_tolerance
        }
