"""
Materials panel for managing sheet inventory.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import List, Dict, Any

logger = logging.getLogger(__name__)


class MaterialsPanel:
    """Panel for managing sheet materials and inventory."""
    
    def __init__(self, parent: tk.Widget, main_window):
        """
        Initialize materials panel.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        self.parent = parent
        self.main_window = main_window
        
        # Panel state
        self.sheet_types = []
        self.available_sheets = []
        
        # Create UI
        self.create_widgets()
        
        logger.info("Materials panel initialized")
    
    def create_widgets(self):
        """Create the panel widgets."""
        # Sheet types section
        types_frame = ttk.LabelFrame(self.parent, text="Sheet Types")
        types_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Treeview for sheet types
        self.types_tree = ttk.Treeview(
            types_frame,
            columns=('width', 'height', 'quantity'),
            show='headings',
            height=6
        )
        
        # Configure columns
        self.types_tree.heading('width', text='Width')
        self.types_tree.heading('height', text='Height')
        self.types_tree.heading('quantity', text='Qty')
        
        self.types_tree.column('width', width=60)
        self.types_tree.column('height', width=60)
        self.types_tree.column('quantity', width=40)
        
        # Scrollbar for treeview
        types_scrollbar = ttk.Scrollbar(types_frame, orient=tk.VERTICAL, command=self.types_tree.yview)
        self.types_tree.configure(yscrollcommand=types_scrollbar.set)
        
        # Pack treeview and scrollbar
        self.types_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        types_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Buttons frame
        buttons_frame = ttk.Frame(self.parent)
        buttons_frame.pack(fill=tk.X, padx=5, pady=5)
        
        # Add sheet button
        ttk.Button(
            buttons_frame,
            text="Add Sheet Type",
            command=self.add_sheet_type
        ).pack(side=tk.LEFT, padx=2)
        
        # Remove sheet button
        ttk.Button(
            buttons_frame,
            text="Remove",
            command=self.remove_sheet_type
        ).pack(side=tk.LEFT, padx=2)
        
        # Edit sheet button
        ttk.Button(
            buttons_frame,
            text="Edit",
            command=self.edit_sheet_type
        ).pack(side=tk.LEFT, padx=2)
        
        # Available sheets section
        available_frame = ttk.LabelFrame(self.parent, text="Available Sheets")
        available_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
        
        # Canvas for visual sheet display
        self.sheets_canvas = tk.Canvas(
            available_frame,
            bg='white',
            height=150
        )
        self.sheets_canvas.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Bind events
        self.types_tree.bind('<Double-1>', self.on_type_double_click)
        self.sheets_canvas.bind('<Button-1>', self.on_sheet_click)
        
        # Load default sheet types
        self.load_default_sheets()
    
    def add_sheet_type(self):
        """Add a new sheet type."""
        self.main_window.add_sheet()
    
    def remove_sheet_type(self):
        """Remove selected sheet type."""
        selection = self.types_tree.selection()
        if selection:
            item = selection[0]
            self.types_tree.delete(item)
            self.refresh_available_sheets()
    
    def edit_sheet_type(self):
        """Edit selected sheet type."""
        selection = self.types_tree.selection()
        if selection:
            # TODO: Implement edit dialog
            pass
    
    def on_type_double_click(self, event):
        """Handle double-click on sheet type."""
        self.edit_sheet_type()
    
    def on_sheet_click(self, event):
        """Handle click on available sheet."""
        # TODO: Implement sheet selection and drag start
        pass
    
    def load_default_sheets(self):
        """Load default sheet types from configuration."""
        try:
            config = self.main_window.config
            default_sheets = config.get('materials', {}).get('default_sheets', [])
            
            for sheet in default_sheets:
                self.add_sheet_type_data(
                    sheet['width'],
                    sheet['height'],
                    sheet['quantity']
                )
        except Exception as e:
            logger.warning(f"Failed to load default sheets: {e}")
    
    def add_sheet_type_data(self, width: float, height: float, quantity: int):
        """
        Add sheet type with data.
        
        Args:
            width: Sheet width
            height: Sheet height
            quantity: Number of sheets
        """
        # Add to treeview
        self.types_tree.insert('', 'end', values=(width, height, quantity))
        
        # Update available sheets
        self.refresh_available_sheets()
    
    def refresh_available_sheets(self):
        """Refresh the available sheets display."""
        # Clear canvas
        self.sheets_canvas.delete('all')
        
        # Get sheet types from treeview
        sheet_types = []
        for item in self.types_tree.get_children():
            values = self.types_tree.item(item)['values']
            sheet_types.append({
                'width': float(values[0]),
                'height': float(values[1]),
                'quantity': int(values[2])
            })
        
        # Draw sheets on canvas
        self.draw_available_sheets(sheet_types)
    
    def draw_available_sheets(self, sheet_types: List[Dict[str, Any]]):
        """
        Draw available sheets on canvas.
        
        Args:
            sheet_types: List of sheet type dictionaries
        """
        canvas_width = self.sheets_canvas.winfo_width()
        canvas_height = self.sheets_canvas.winfo_height()
        
        if canvas_width <= 1 or canvas_height <= 1:
            # Canvas not ready yet
            self.parent.after(100, lambda: self.draw_available_sheets(sheet_types))
            return
        
        # Calculate scale to fit sheets
        max_width = max((sheet['width'] for sheet in sheet_types), default=1200)
        max_height = max((sheet['height'] for sheet in sheet_types), default=2400)
        
        scale_x = (canvas_width - 20) / max_width
        scale_y = (canvas_height - 20) / max_height
        scale = min(scale_x, scale_y) * 0.8  # Leave some margin
        
        # Draw each sheet type
        x_offset = 10
        for sheet in sheet_types:
            width = sheet['width'] * scale
            height = sheet['height'] * scale
            quantity = sheet['quantity']
            
            # Draw multiple sheets stacked
            for i in range(min(quantity, 5)):  # Show max 5 stacked
                offset = i * 2
                
                # Draw sheet rectangle
                self.sheets_canvas.create_rectangle(
                    x_offset + offset,
                    10 + offset,
                    x_offset + width + offset,
                    10 + height + offset,
                    outline='black',
                    fill='lightblue',
                    tags=f'sheet_{len(self.sheet_types)}'
                )
            
            # Draw quantity label
            self.sheets_canvas.create_text(
                x_offset + width/2,
                10 + height + 15,
                text=f"{int(sheet['width'])}×{int(sheet['height'])}\n({quantity})",
                font=('Arial', 8),
                justify=tk.CENTER
            )
            
            x_offset += width + 20
    
    def refresh(self):
        """Refresh the entire panel."""
        self.refresh_available_sheets()
    
    def get_sheet_types(self) -> List[Dict[str, Any]]:
        """
        Get current sheet types.
        
        Returns:
            List of sheet type dictionaries
        """
        sheet_types = []
        for item in self.types_tree.get_children():
            values = self.types_tree.item(item)['values']
            sheet_types.append({
                'width': float(values[0]),
                'height': float(values[1]),
                'quantity': int(values[2])
            })
        return sheet_types
