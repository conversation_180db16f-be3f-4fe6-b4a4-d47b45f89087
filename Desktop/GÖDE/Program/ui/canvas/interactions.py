"""
Canvas interaction handling for drag-and-drop and snapping.
"""

import logging
from typing import Op<PERSON>, Tu<PERSON>, Dict, Any, List
from shapely.geometry import Polygon
import tkinter as tk

logger = logging.getLogger(__name__)


class DragManager:
    """
    Manages drag-and-drop operations on canvas objects with snapping support.
    
    Features:
    - Object selection and dragging
    - Snap-to-object functionality
    - Visual feedback during drag operations
    - Multi-object selection support
    """
    
    def __init__(self, canvas):
        """
        Initialize drag manager.
        
        Args:
            canvas: InfiniteCanvas instance
        """
        self.canvas = canvas
        self.dragging = False
        self.drag_item = None
        self.drag_start_x = 0
        self.drag_start_y = 0
        self.drag_offset_x = 0
        self.drag_offset_y = 0
        
        # Snap settings
        self.snap_enabled = True
        self.snap_tolerance = 5.0  # mm
        
        # Visual feedback
        self.snap_indicators = []
        
        logger.info("DragManager initialized")
    
    def start_drag(self, item_id: int, start_x: float, start_y: float) -> bool:
        """
        Start dragging an object.
        
        Args:
            item_id: Canvas item ID to drag
            start_x, start_y: Starting canvas coordinates
            
        Returns:
            True if drag started successfully
        """
        if self.dragging:
            return False
        
        # Check if item exists and is draggable
        if item_id not in self.canvas.canvas_objects:
            return False
        
        # Get item bounds for offset calculation
        bbox = self.canvas.canvas.bbox(item_id)
        if not bbox:
            return False
        
        # Calculate offset from item center
        item_center_x = (bbox[0] + bbox[2]) / 2
        item_center_y = (bbox[1] + bbox[3]) / 2
        
        self.drag_item = item_id
        self.drag_start_x = start_x
        self.drag_start_y = start_y
        self.drag_offset_x = start_x - item_center_x
        self.drag_offset_y = start_y - item_center_y
        self.dragging = True
        
        # Visual feedback - highlight the dragged item
        self.canvas.canvas.itemconfig(item_id, width=3)
        
        logger.debug(f"Started dragging item {item_id} at ({start_x}, {start_y})")
        return True
    
    def update_drag(self, current_x: float, current_y: float) -> bool:
        """
        Update drag position.
        
        Args:
            current_x, current_y: Current canvas coordinates
            
        Returns:
            True if drag was updated
        """
        if not self.dragging or not self.drag_item:
            return False
        
        # Calculate movement delta
        dx = current_x - self.drag_start_x
        dy = current_y - self.drag_start_y
        
        # Apply snapping if enabled
        if self.snap_enabled:
            snap_dx, snap_dy = self._calculate_snap_delta(current_x, current_y)
            dx += snap_dx
            dy += snap_dy
        
        # Move the item
        self.canvas.canvas.move(self.drag_item, dx, dy)
        
        # Update drag start position for next delta calculation
        self.drag_start_x = current_x
        self.drag_start_y = current_y
        
        return True
    
    def end_drag(self, end_x: float, end_y: float) -> bool:
        """
        End the drag operation.
        
        Args:
            end_x, end_y: Final canvas coordinates
            
        Returns:
            True if drag ended successfully
        """
        if not self.dragging or not self.drag_item:
            return False
        
        # Final position update
        self.update_drag(end_x, end_y)
        
        # Remove visual feedback
        self.canvas.canvas.itemconfig(self.drag_item, width=1)
        self._clear_snap_indicators()
        
        # Update object geometry in canvas object tracking
        self._update_object_geometry()
        
        # Reset drag state
        self.dragging = False
        self.drag_item = None
        
        logger.debug(f"Ended drag at ({end_x}, {end_y})")
        return True
    
    def cancel_drag(self) -> bool:
        """
        Cancel the current drag operation and return object to original position.
        
        Returns:
            True if drag was cancelled
        """
        if not self.dragging or not self.drag_item:
            return False
        
        # Calculate movement back to original position
        current_bbox = self.canvas.canvas.bbox(self.drag_item)
        if current_bbox:
            current_center_x = (current_bbox[0] + current_bbox[2]) / 2
            current_center_y = (current_bbox[1] + current_bbox[3]) / 2
            
            # Move back to original position
            dx = self.drag_start_x - self.drag_offset_x - current_center_x
            dy = self.drag_start_y - self.drag_offset_y - current_center_y
            
            self.canvas.canvas.move(self.drag_item, dx, dy)
        
        # Remove visual feedback
        self.canvas.canvas.itemconfig(self.drag_item, width=1)
        self._clear_snap_indicators()
        
        # Reset drag state
        self.dragging = False
        self.drag_item = None
        
        logger.debug("Drag cancelled")
        return True
    
    def _calculate_snap_delta(self, current_x: float, current_y: float) -> Tuple[float, float]:
        """
        Calculate snap delta for current position.
        
        Args:
            current_x, current_y: Current canvas coordinates
            
        Returns:
            Tuple of (dx, dy) snap adjustment
        """
        if not self.drag_item:
            return 0.0, 0.0
        
        # Get current item bounds
        bbox = self.canvas.canvas.bbox(self.drag_item)
        if not bbox:
            return 0.0, 0.0
        
        # Check for snap targets
        snap_dx, snap_dy = 0.0, 0.0
        min_snap_distance = float('inf')
        
        # Get all other objects for snapping
        for item_id, obj_data in self.canvas.canvas_objects.items():
            if item_id == self.drag_item:
                continue
            
            target_bbox = self.canvas.canvas.bbox(item_id)
            if not target_bbox:
                continue
            
            # Calculate snap delta using the stub function
            delta = get_snap_delta(bbox, target_bbox, self.snap_tolerance)
            if delta:
                dx, dy = delta
                distance = (dx * dx + dy * dy) ** 0.5
                
                if distance < min_snap_distance:
                    min_snap_distance = distance
                    snap_dx, snap_dy = dx, dy
        
        # Show snap indicators if snapping
        if snap_dx != 0 or snap_dy != 0:
            self._show_snap_indicators(current_x + snap_dx, current_y + snap_dy)
        else:
            self._clear_snap_indicators()
        
        return snap_dx, snap_dy
    
    def _show_snap_indicators(self, snap_x: float, snap_y: float):
        """Show visual snap indicators."""
        self._clear_snap_indicators()
        
        # Draw snap crosshairs
        size = 10
        self.snap_indicators.append(
            self.canvas.canvas.create_line(
                snap_x - size, snap_y, snap_x + size, snap_y,
                fill='red', width=2, tags='snap_indicator'
            )
        )
        self.snap_indicators.append(
            self.canvas.canvas.create_line(
                snap_x, snap_y - size, snap_x, snap_y + size,
                fill='red', width=2, tags='snap_indicator'
            )
        )
    
    def _clear_snap_indicators(self):
        """Clear visual snap indicators."""
        for indicator in self.snap_indicators:
            self.canvas.canvas.delete(indicator)
        self.snap_indicators.clear()
    
    def _update_object_geometry(self):
        """Update the stored geometry for the dragged object."""
        if not self.drag_item or self.drag_item not in self.canvas.canvas_objects:
            return
        
        # Get current canvas coordinates
        coords = self.canvas.canvas.coords(self.drag_item)
        if not coords:
            return
        
        # Convert flat coordinate list to pairs
        coord_pairs = [(coords[i], coords[i+1]) for i in range(0, len(coords), 2)]
        
        # Update stored geometry
        try:
            new_polygon = Polygon(coord_pairs)
            self.canvas.canvas_objects[self.drag_item]['geometry'] = new_polygon
        except Exception as e:
            logger.warning(f"Failed to update object geometry: {e}")
    
    def set_snap_settings(self, enabled: bool, tolerance: float):
        """
        Update snap settings.
        
        Args:
            enabled: Whether snapping is enabled
            tolerance: Snap tolerance in mm
        """
        self.snap_enabled = enabled
        self.snap_tolerance = tolerance
        logger.debug(f"Snap settings updated: enabled={enabled}, tolerance={tolerance}")


def get_snap_delta(item_bbox: Tuple[float, float, float, float], 
                   target_bbox: Tuple[float, float, float, float], 
                   tolerance_mm: float) -> Optional[Tuple[float, float]]:
    """
    Calculate snap delta between two bounding boxes.
    
    This is a stub function that implements basic edge-to-edge snapping.
    In a full implementation, this would handle:
    - Corner-to-corner snapping
    - Edge-to-edge snapping
    - Center-to-center snapping
    - Grid snapping
    
    Args:
        item_bbox: Bounding box of item being dragged (x1, y1, x2, y2)
        target_bbox: Bounding box of snap target (x1, y1, x2, y2)
        tolerance_mm: Snap tolerance in millimeters
        
    Returns:
        Tuple of (dx, dy) snap delta, or None if no snap
    """
    if not item_bbox or not target_bbox:
        return None
    
    item_x1, item_y1, item_x2, item_y2 = item_bbox
    target_x1, target_y1, target_x2, target_y2 = target_bbox
    
    # Calculate item and target centers and edges
    item_center_x = (item_x1 + item_x2) / 2
    item_center_y = (item_y1 + item_y2) / 2
    
    target_center_x = (target_x1 + target_x2) / 2
    target_center_y = (target_y1 + target_y2) / 2
    
    # Check various snap points
    snap_candidates = []
    
    # Edge-to-edge snapping (horizontal)
    snap_candidates.extend([
        (target_x1 - item_x2, 0),  # Right edge of item to left edge of target
        (target_x2 - item_x1, 0),  # Left edge of item to right edge of target
        (target_center_x - item_center_x, 0),  # Center-to-center horizontal
    ])
    
    # Edge-to-edge snapping (vertical)
    snap_candidates.extend([
        (0, target_y1 - item_y2),  # Bottom edge of item to top edge of target
        (0, target_y2 - item_y1),  # Top edge of item to bottom edge of target
        (0, target_center_y - item_center_y),  # Center-to-center vertical
    ])
    
    # Find the closest snap within tolerance
    best_snap = None
    min_distance = tolerance_mm
    
    for dx, dy in snap_candidates:
        distance = (dx * dx + dy * dy) ** 0.5
        if distance < min_distance:
            min_distance = distance
            best_snap = (dx, dy)
    
    return best_snap
