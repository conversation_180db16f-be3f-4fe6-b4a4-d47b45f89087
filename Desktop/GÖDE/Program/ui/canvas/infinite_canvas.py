"""
Infinite canvas with zoom and pan capabilities.
"""

import tkinter as tk
from tkinter import ttk
import logging
import math
from typing import Optional, Tuple, List, Any, Dict
from shapely.geometry import Polygon, Point as ShapelyPoint
from shapely.geometry.base import BaseGeometry

from .interactions import DragManager

logger = logging.getLogger(__name__)


class InfiniteCanvas:
    """
    Infinite canvas widget with zoom, pan, and polygon rendering.

    Features:
    - Mouse wheel zoom with zoom-to-point
    - Click and drag panning
    - Shapely polygon rendering with proper scaling
    - Grid display with zoom-adaptive spacing
    - Object selection and manipulation
    """

    def __init__(self, parent: tk.Widget, main_window=None):
        """
        Initialize the infinite canvas.

        Args:
            parent: Parent widget
            main_window: Reference to main window (optional)
        """
        self.parent = parent
        self.main_window = main_window

        # Canvas properties
        self.zoom_level = 1.0
        self.pan_x = 0.0
        self.pan_y = 0.0
        self.grid_enabled = True
        self.grid_spacing = 50  # Base grid spacing in pixels

        # Interaction state
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.panning = False
        self.selected_objects = []

        # Object tracking
        self.canvas_objects = {}  # item_id -> object_data mapping
        self.object_counter = 0

        # Drag manager for object manipulation
        from .interactions import DragManager
        self.drag_manager = DragManager(self)

        # Create the canvas widget
        self.create_canvas()
        self.bind_events()

        logger.info("Infinite canvas initialized")
    
    def create_canvas(self):
        """Create the canvas widget with scrollbars."""
        # Create frame for canvas and scrollbars
        canvas_frame = ttk.Frame(self.parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)

        # Create canvas with large scroll region for infinite feel
        self.canvas = tk.Canvas(
            canvas_frame,
            bg='#f8f9fa',
            scrollregion=(-10000, -10000, 10000, 10000),
            highlightthickness=0
        )

        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)

        # Configure canvas scrolling
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)

        # Pack widgets
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')

        # Configure grid weights
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)

        # Center the view initially
        self.canvas.xview_moveto(0.5)
        self.canvas.yview_moveto(0.5)

        # Draw initial grid
        self.draw_grid()
    
    def bind_events(self):
        """Bind mouse and keyboard events."""
        # Mouse events
        self.canvas.bind('<Button-1>', self.on_mouse_press)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_release)
        self.canvas.bind('<Button-3>', self.on_right_click)
        
        # Mouse wheel for zooming
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Button-4>', self.on_mouse_wheel)  # Linux
        self.canvas.bind('<Button-5>', self.on_mouse_wheel)  # Linux
        
        # Keyboard events
        self.canvas.bind('<Key>', self.on_key_press)
        self.canvas.focus_set()  # Allow keyboard focus
    
    def on_mouse_press(self, event):
        """Handle mouse press events."""
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y

        # Convert to canvas coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        # Check for object at click point
        clicked_item = self.get_object_at_point(canvas_x, canvas_y)

        if clicked_item:
            # Object clicked - start object drag
            if self.drag_manager.start_drag(clicked_item, canvas_x, canvas_y):
                logger.debug(f"Started dragging object {clicked_item} at ({canvas_x}, {canvas_y})")
            else:
                # Fallback to panning if drag start failed
                self.panning = True
                self.canvas.scan_mark(event.x, event.y)
        else:
            # Empty space clicked - start canvas panning
            self.panning = True
            self.canvas.scan_mark(event.x, event.y)

        logger.debug(f"Mouse press at canvas coordinates: ({canvas_x}, {canvas_y})")

    def on_mouse_drag(self, event):
        """Handle mouse drag events."""
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        if self.drag_manager.dragging:
            # Update object drag
            self.drag_manager.update_drag(canvas_x, canvas_y)
        elif self.panning:
            # Pan the canvas
            self.canvas.scan_dragto(event.x, event.y, gain=1)
            # Redraw grid after panning
            self.draw_grid()

        self.last_mouse_x = event.x
        self.last_mouse_y = event.y

    def on_mouse_release(self, event):
        """Handle mouse release events."""
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)

        if self.drag_manager.dragging:
            # End object drag
            self.drag_manager.end_drag(canvas_x, canvas_y)

        self.panning = False

    def on_right_click(self, event):
        """Handle right-click context menu."""
        # Cancel any ongoing drag
        if self.drag_manager.dragging:
            self.drag_manager.cancel_drag()

        # TODO: Show context menu
        logger.debug("Right-click context menu (not implemented)")

    def on_key_press(self, event):
        """Handle key press events."""
        if event.keysym == 'Escape':
            # Cancel drag operation
            if self.drag_manager.dragging:
                self.drag_manager.cancel_drag()
        elif event.keysym == 'Delete':
            self.delete_selected()
        elif event.keysym == 'Return':
            if self.main_window:
                self.main_window.cut_selected()
    
    def on_mouse_wheel(self, event):
        """Handle mouse wheel for zooming."""
        # Get mouse position
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        # Determine zoom direction
        if event.delta > 0 or event.num == 4:
            self.zoom_at_point(x, y, 1.1)
        elif event.delta < 0 or event.num == 5:
            self.zoom_at_point(x, y, 0.9)
    
    def on_key_press(self, event):
        """Handle key press events."""
        if event.keysym == 'Delete':
            self.delete_selected()
        elif event.keysym == 'Return':
            self.main_window.cut_selected()
    
    def zoom_at_point(self, x: float, y: float, factor: float):
        """
        Zoom at a specific point.

        Args:
            x, y: Point to zoom at in canvas coordinates
            factor: Zoom factor (>1 to zoom in, <1 to zoom out)
        """
        new_zoom = self.zoom_level * factor

        # Clamp zoom level
        min_zoom = 0.05
        max_zoom = 20.0
        new_zoom = max(min_zoom, min(max_zoom, new_zoom))

        if new_zoom != self.zoom_level:
            # Scale all objects except grid
            scale_factor = new_zoom / self.zoom_level

            # Scale all objects that are not grid
            for item in self.canvas.find_all():
                tags = self.canvas.gettags(item)
                if 'grid' not in tags:
                    self.canvas.scale(item, x, y, scale_factor, scale_factor)

            self.zoom_level = new_zoom
            self.update_zoom_display()

            # Redraw grid at new zoom level
            self.draw_grid()

    def draw_polygon(self, polygon: Polygon, **options) -> int:
        """
        Draw a Shapely polygon on the canvas.

        Args:
            polygon: Shapely Polygon object to draw
            **options: Canvas drawing options (fill, outline, width, etc.)

        Returns:
            Canvas item ID of the drawn polygon
        """
        if not isinstance(polygon, Polygon):
            raise ValueError("Expected Shapely Polygon object")

        # Get polygon coordinates
        coords = list(polygon.exterior.coords)

        # Convert to flat list for tkinter (x1, y1, x2, y2, ...)
        flat_coords = []
        for x, y in coords:
            flat_coords.extend([x, y])

        # Set default drawing options
        default_options = {
            'outline': 'black',
            'fill': '',
            'width': 1,
            'tags': ('polygon',)
        }
        default_options.update(options)

        # Create polygon on canvas
        item_id = self.canvas.create_polygon(flat_coords, **default_options)

        # Store object data
        self.object_counter += 1
        self.canvas_objects[item_id] = {
            'type': 'polygon',
            'geometry': polygon,
            'object_id': self.object_counter
        }

        logger.debug(f"Drew polygon with {len(coords)} vertices, item_id: {item_id}")
        return item_id
    
    def zoom_in(self):
        """Zoom in at center of view."""
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        canvas_x = self.canvas.canvasx(center_x)
        canvas_y = self.canvas.canvasy(center_y)
        self.zoom_at_point(canvas_x, canvas_y, 1.2)
    
    def zoom_out(self):
        """Zoom out at center of view."""
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        canvas_x = self.canvas.canvasx(center_x)
        canvas_y = self.canvas.canvasy(center_y)
        self.zoom_at_point(canvas_x, canvas_y, 0.8)
    
    def zoom_fit(self):
        """Fit all objects in view."""
        # Get bounding box of all objects
        bbox = self.canvas.bbox('all')
        if bbox:
            x1, y1, x2, y2 = bbox
            
            # Calculate required zoom to fit
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 0 and canvas_height > 0:
                zoom_x = canvas_width / (x2 - x1)
                zoom_y = canvas_height / (y2 - y1)
                zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% to leave some margin
                
                # Center the view
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                self.zoom_level = zoom_factor
                self.canvas.scale('all', center_x, center_y, zoom_factor, zoom_factor)
                
                self.update_zoom_display()
                self.draw_grid()
    
    def toggle_grid(self):
        """Toggle grid display."""
        self.grid_enabled = not self.grid_enabled
        if self.grid_enabled:
            self.draw_grid()
        else:
            self.canvas.delete('grid')
    
    def draw_grid(self):
        """Draw adaptive grid on canvas."""
        if not self.grid_enabled:
            return

        # Clear existing grid
        self.canvas.delete('grid')

        # Get visible area
        x1 = self.canvas.canvasx(0)
        y1 = self.canvas.canvasy(0)
        x2 = self.canvas.canvasx(self.canvas.winfo_width())
        y2 = self.canvas.canvasy(self.canvas.winfo_height())

        # Calculate adaptive grid spacing
        spacing = self._calculate_grid_spacing()

        if spacing <= 0:
            return

        # Draw vertical lines
        start_x = math.floor(x1 / spacing) * spacing
        x = start_x
        while x <= x2:
            self.canvas.create_line(x, y1, x, y2, fill='#e0e0e0', tags='grid', width=1)
            x += spacing

        # Draw horizontal lines
        start_y = math.floor(y1 / spacing) * spacing
        y = start_y
        while y <= y2:
            self.canvas.create_line(x1, y, x2, y, fill='#e0e0e0', tags='grid', width=1)
            y += spacing

    def _calculate_grid_spacing(self) -> float:
        """
        Calculate appropriate grid spacing based on zoom level.

        Returns:
            Grid spacing in canvas units, or 0 if grid should not be drawn
        """
        # Base spacing in world coordinates (mm)
        base_spacings = [1, 2, 5, 10, 20, 50, 100, 200, 500, 1000, 2000, 5000]

        # Target spacing in screen pixels
        target_pixel_spacing = 20

        # Current spacing in screen pixels for each base spacing
        for base_spacing in base_spacings:
            pixel_spacing = base_spacing * self.zoom_level
            if pixel_spacing >= target_pixel_spacing:
                return base_spacing

        # If zoom is very high, use the largest spacing
        return base_spacings[-1]
    
    def clear(self):
        """Clear all objects from canvas."""
        self.canvas.delete('all')
        self.selected_objects.clear()
        self.draw_grid()
    
    def delete_selected(self):
        """Delete selected objects."""
        for obj_id in self.selected_objects:
            self.canvas.delete(obj_id)
        self.selected_objects.clear()
    
    def update_zoom_display(self):
        """Update zoom level display in status bar."""
        if self.main_window and hasattr(self.main_window, 'zoom_label'):
            zoom_percent = int(self.zoom_level * 100)
            self.main_window.zoom_label.config(text=f"Zoom: {zoom_percent}%")

    def get_canvas_bounds(self) -> Tuple[float, float, float, float]:
        """
        Get the current visible canvas bounds.

        Returns:
            Tuple of (x1, y1, x2, y2) in canvas coordinates
        """
        x1 = self.canvas.canvasx(0)
        y1 = self.canvas.canvasy(0)
        x2 = self.canvas.canvasx(self.canvas.winfo_width())
        y2 = self.canvas.canvasy(self.canvas.winfo_height())
        return x1, y1, x2, y2

    def screen_to_canvas(self, screen_x: float, screen_y: float) -> Tuple[float, float]:
        """
        Convert screen coordinates to canvas coordinates.

        Args:
            screen_x, screen_y: Screen coordinates

        Returns:
            Tuple of canvas coordinates
        """
        canvas_x = self.canvas.canvasx(screen_x)
        canvas_y = self.canvas.canvasy(screen_y)
        return canvas_x, canvas_y

    def canvas_to_screen(self, canvas_x: float, canvas_y: float) -> Tuple[float, float]:
        """
        Convert canvas coordinates to screen coordinates.

        Args:
            canvas_x, canvas_y: Canvas coordinates

        Returns:
            Tuple of screen coordinates
        """
        # This is an approximation - exact conversion requires more complex math
        screen_x = canvas_x - self.canvas.canvasx(0)
        screen_y = canvas_y - self.canvas.canvasy(0)
        return screen_x, screen_y

    def get_object_at_point(self, canvas_x: float, canvas_y: float) -> Optional[int]:
        """
        Get the topmost object at a canvas point.

        Args:
            canvas_x, canvas_y: Canvas coordinates

        Returns:
            Canvas item ID or None if no object found
        """
        items = self.canvas.find_overlapping(canvas_x-2, canvas_y-2, canvas_x+2, canvas_y+2)

        # Filter out grid items and return topmost
        for item in reversed(items):  # Reversed to get topmost first
            tags = self.canvas.gettags(item)
            if 'grid' not in tags:
                return item

        return None

    def delete_object(self, item_id: int) -> bool:
        """
        Delete an object from the canvas.

        Args:
            item_id: Canvas item ID

        Returns:
            True if object was deleted
        """
        if item_id in self.canvas_objects:
            self.canvas.delete(item_id)
            del self.canvas_objects[item_id]
            return True
        return False

    def get_all_objects(self) -> Dict[int, Dict[str, Any]]:
        """
        Get all objects on the canvas.

        Returns:
            Dictionary mapping item IDs to object data
        """
        return self.canvas_objects.copy()
