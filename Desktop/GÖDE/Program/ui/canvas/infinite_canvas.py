"""
Infinite canvas with zoom and pan capabilities.
"""

import tkinter as tk
from tkinter import ttk
import logging
from typing import Optional, Tuple, List, Any

logger = logging.getLogger(__name__)


class InfiniteCanvas:
    """
    Infinite canvas widget with zoom, pan, and object manipulation.
    """
    
    def __init__(self, parent: tk.Widget, main_window):
        """
        Initialize the infinite canvas.
        
        Args:
            parent: Parent widget
            main_window: Reference to main window
        """
        self.parent = parent
        self.main_window = main_window
        
        # Canvas properties
        self.zoom_level = 1.0
        self.pan_x = 0.0
        self.pan_y = 0.0
        self.grid_enabled = True
        
        # Interaction state
        self.last_mouse_x = 0
        self.last_mouse_y = 0
        self.dragging = False
        self.selected_objects = []
        
        # Create the canvas widget
        self.create_canvas()
        self.bind_events()
        
        logger.info("Infinite canvas initialized")
    
    def create_canvas(self):
        """Create the canvas widget with scrollbars."""
        # Create frame for canvas and scrollbars
        canvas_frame = ttk.Frame(self.parent)
        canvas_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create canvas
        self.canvas = tk.Canvas(
            canvas_frame,
            bg='#f0f0f0',
            scrollregion=(-5000, -5000, 5000, 5000)
        )
        
        # Create scrollbars
        v_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.VERTICAL, command=self.canvas.yview)
        h_scrollbar = ttk.Scrollbar(canvas_frame, orient=tk.HORIZONTAL, command=self.canvas.xview)
        
        # Configure canvas scrolling
        self.canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack widgets
        self.canvas.grid(row=0, column=0, sticky='nsew')
        v_scrollbar.grid(row=0, column=1, sticky='ns')
        h_scrollbar.grid(row=1, column=0, sticky='ew')
        
        # Configure grid weights
        canvas_frame.grid_rowconfigure(0, weight=1)
        canvas_frame.grid_columnconfigure(0, weight=1)
        
        # Draw initial grid
        self.draw_grid()
    
    def bind_events(self):
        """Bind mouse and keyboard events."""
        # Mouse events
        self.canvas.bind('<Button-1>', self.on_mouse_press)
        self.canvas.bind('<B1-Motion>', self.on_mouse_drag)
        self.canvas.bind('<ButtonRelease-1>', self.on_mouse_release)
        self.canvas.bind('<Button-3>', self.on_right_click)
        
        # Mouse wheel for zooming
        self.canvas.bind('<MouseWheel>', self.on_mouse_wheel)
        self.canvas.bind('<Button-4>', self.on_mouse_wheel)  # Linux
        self.canvas.bind('<Button-5>', self.on_mouse_wheel)  # Linux
        
        # Keyboard events
        self.canvas.bind('<Key>', self.on_key_press)
        self.canvas.focus_set()  # Allow keyboard focus
    
    def on_mouse_press(self, event):
        """Handle mouse press events."""
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
        
        # Convert to canvas coordinates
        canvas_x = self.canvas.canvasx(event.x)
        canvas_y = self.canvas.canvasy(event.y)
        
        # Check for object selection
        clicked_item = self.canvas.find_closest(canvas_x, canvas_y)[0]
        
        # Start dragging
        self.dragging = True
        
        logger.debug(f"Mouse press at canvas coordinates: ({canvas_x}, {canvas_y})")
    
    def on_mouse_drag(self, event):
        """Handle mouse drag events."""
        if not self.dragging:
            return
        
        dx = event.x - self.last_mouse_x
        dy = event.y - self.last_mouse_y
        
        # Pan the canvas
        self.canvas.scan_dragto(event.x, event.y, gain=1)
        
        self.last_mouse_x = event.x
        self.last_mouse_y = event.y
    
    def on_mouse_release(self, event):
        """Handle mouse release events."""
        self.dragging = False
    
    def on_right_click(self, event):
        """Handle right-click context menu."""
        # TODO: Implement context menu
        pass
    
    def on_mouse_wheel(self, event):
        """Handle mouse wheel for zooming."""
        # Get mouse position
        x = self.canvas.canvasx(event.x)
        y = self.canvas.canvasy(event.y)
        
        # Determine zoom direction
        if event.delta > 0 or event.num == 4:
            self.zoom_at_point(x, y, 1.1)
        elif event.delta < 0 or event.num == 5:
            self.zoom_at_point(x, y, 0.9)
    
    def on_key_press(self, event):
        """Handle key press events."""
        if event.keysym == 'Delete':
            self.delete_selected()
        elif event.keysym == 'Return':
            self.main_window.cut_selected()
    
    def zoom_at_point(self, x: float, y: float, factor: float):
        """
        Zoom at a specific point.
        
        Args:
            x, y: Point to zoom at
            factor: Zoom factor (>1 to zoom in, <1 to zoom out)
        """
        new_zoom = self.zoom_level * factor
        
        # Clamp zoom level
        min_zoom = 0.1
        max_zoom = 10.0
        new_zoom = max(min_zoom, min(max_zoom, new_zoom))
        
        if new_zoom != self.zoom_level:
            # Scale all objects
            scale_factor = new_zoom / self.zoom_level
            self.canvas.scale('all', x, y, scale_factor, scale_factor)
            
            self.zoom_level = new_zoom
            self.update_zoom_display()
            
            # Redraw grid at new zoom level
            self.draw_grid()
    
    def zoom_in(self):
        """Zoom in at center of view."""
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        canvas_x = self.canvas.canvasx(center_x)
        canvas_y = self.canvas.canvasy(center_y)
        self.zoom_at_point(canvas_x, canvas_y, 1.2)
    
    def zoom_out(self):
        """Zoom out at center of view."""
        center_x = self.canvas.winfo_width() / 2
        center_y = self.canvas.winfo_height() / 2
        canvas_x = self.canvas.canvasx(center_x)
        canvas_y = self.canvas.canvasy(center_y)
        self.zoom_at_point(canvas_x, canvas_y, 0.8)
    
    def zoom_fit(self):
        """Fit all objects in view."""
        # Get bounding box of all objects
        bbox = self.canvas.bbox('all')
        if bbox:
            x1, y1, x2, y2 = bbox
            
            # Calculate required zoom to fit
            canvas_width = self.canvas.winfo_width()
            canvas_height = self.canvas.winfo_height()
            
            if canvas_width > 0 and canvas_height > 0:
                zoom_x = canvas_width / (x2 - x1)
                zoom_y = canvas_height / (y2 - y1)
                zoom_factor = min(zoom_x, zoom_y) * 0.9  # 90% to leave some margin
                
                # Center the view
                center_x = (x1 + x2) / 2
                center_y = (y1 + y2) / 2
                
                self.zoom_level = zoom_factor
                self.canvas.scale('all', center_x, center_y, zoom_factor, zoom_factor)
                
                self.update_zoom_display()
                self.draw_grid()
    
    def toggle_grid(self):
        """Toggle grid display."""
        self.grid_enabled = not self.grid_enabled
        if self.grid_enabled:
            self.draw_grid()
        else:
            self.canvas.delete('grid')
    
    def draw_grid(self):
        """Draw grid on canvas."""
        if not self.grid_enabled:
            return
        
        # Clear existing grid
        self.canvas.delete('grid')
        
        # Get visible area
        x1 = self.canvas.canvasx(0)
        y1 = self.canvas.canvasy(0)
        x2 = self.canvas.canvasx(self.canvas.winfo_width())
        y2 = self.canvas.canvasy(self.canvas.winfo_height())
        
        # Grid spacing (adjust based on zoom)
        base_spacing = 50
        spacing = base_spacing * self.zoom_level
        
        # Don't draw grid if too dense or too sparse
        if spacing < 5 or spacing > 200:
            return
        
        # Draw vertical lines
        start_x = int(x1 / spacing) * spacing
        x = start_x
        while x <= x2:
            self.canvas.create_line(x, y1, x, y2, fill='#d0d0d0', tags='grid')
            x += spacing
        
        # Draw horizontal lines
        start_y = int(y1 / spacing) * spacing
        y = start_y
        while y <= y2:
            self.canvas.create_line(x1, y, x2, y, fill='#d0d0d0', tags='grid')
            y += spacing
    
    def clear(self):
        """Clear all objects from canvas."""
        self.canvas.delete('all')
        self.selected_objects.clear()
        self.draw_grid()
    
    def delete_selected(self):
        """Delete selected objects."""
        for obj_id in self.selected_objects:
            self.canvas.delete(obj_id)
        self.selected_objects.clear()
    
    def update_zoom_display(self):
        """Update zoom level display in status bar."""
        zoom_percent = int(self.zoom_level * 100)
        self.main_window.zoom_label.config(text=f"Zoom: {zoom_percent}%")
    
    def add_template(self, template):
        """
        Add a template to the canvas.
        
        Args:
            template: Template object to add
        """
        # TODO: Implement template rendering
        pass
    
    def add_sheet(self, sheet):
        """
        Add a sheet to the canvas.
        
        Args:
            sheet: Sheet object to add
        """
        # TODO: Implement sheet rendering
        pass
