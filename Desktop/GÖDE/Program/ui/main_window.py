"""
Main application window for the drywall optimization tool.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
import json
from datetime import datetime
from typing import Optional, Dict, Any

from .canvas.infinite_canvas import InfiniteCanvas
from .panels.materials import MaterialsPanel
from .panels.properties import PropertiesPanel
from .dialogs.import_dxf import ImportDXFDialog
from .dialogs.add_sheet import AddSheetDialog
from .dialogs.settings import SettingsDialog
from .dialogs.performance_stats import PerformanceStatsDialog

from core.dxf_import.converter import DXFConverter
from core.geometry.spatial import SpatialIndex
from core.cutting.engine import CuttingEngine
from core.cutting.metadata import MetadataTracker
from export.pdf import PDFExporter
from persistence import JSONProjectHandler
from core.performance import MemoryManager, PerformanceMonitor
from utils.validation import validate_file_path


logger = logging.getLogger(__name__)


class MainWindow:
    """Main application window."""
    
    def __init__(self, root: tk.Tk):
        """
        Initialize main window.
        
        Args:
            root: Tkinter root window
        """
        self.root = root

        # Load configuration
        self.load_config()

        self.setup_window()

        # Initialize components
        self.dxf_converter = DXFConverter()
        self.spatial_index = SpatialIndex()

        # Initialize cutting engine
        cutting_config = self.config.get('cutting', {})
        self.cutting_engine = CuttingEngine(cutting_config)
        self.metadata_tracker = MetadataTracker()

        # Initialize export and persistence
        export_config = self.config.get('export', {})
        self.pdf_exporter = PDFExporter(export_config)

        persistence_config = self.config.get('persistence', {})
        self.project_handler = JSONProjectHandler(persistence_config)

        # Initialize performance monitoring
        memory_config = self.config.get('memory', {})
        self.memory_manager = MemoryManager(
            max_cache_size=memory_config.get('max_cache_size', 1000),
            memory_threshold_mb=memory_config.get('memory_threshold_mb', 500.0)
        )
        self.performance_monitor = PerformanceMonitor(max_history=1000)

        # Application state
        self.project_file = None
        self.project_modified = False
        self.templates = []
        self.sheets = []
        self.cuts = []
        self.selected_template = None
        self.selected_sheet = None
        
        # Load configuration
        self.config = self.load_config()
        
        # Create UI
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()
        
        # Bind events
        self.bind_events()
        
        logger.info("Main window initialized")

    def load_config(self):
        """Load application configuration."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "settings.json"
            if config_path.exists():
                with open(config_path, 'r') as f:
                    self.config = json.load(f)
            else:
                # Default configuration
                self.config = {
                    'cutting': {'precision_mm': 0.1},
                    'export': {'pdf': {'page_size': 'A4'}},
                    'persistence': {'create_backups': True}
                }
        except Exception as e:
            logger.warning(f"Failed to load config: {e}")
            self.config = {}

    def setup_window(self):
        """Configure the main window."""
        self.root.title("Drywall Optimization Tool")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Configure window icon (if available)
        try:
            # You can add an icon file later
            pass
        except:
            pass
    

    
    def create_menu(self):
        """Create the menu bar."""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Project", command=self.new_project, accelerator="Ctrl+N")
        file_menu.add_command(label="Open Project", command=self.open_project, accelerator="Ctrl+O")
        file_menu.add_command(label="Save Project", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As...", command=self.save_project_as, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="Import DXF", command=self.import_dxf, accelerator="Ctrl+I")
        file_menu.add_separator()
        file_menu.add_command(label="Export PDF", command=self.export_pdf, accelerator="Ctrl+E")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_exit, accelerator="Ctrl+Q")
        
        # Edit menu
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_separator()
        edit_menu.add_command(label="Add Sheet", command=self.add_sheet, accelerator="Ctrl+A")
        edit_menu.add_command(label="Delete Selected", command=self.delete_selected, accelerator="Delete")
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="Zoom Fit", command=self.zoom_fit, accelerator="Ctrl+0")
        view_menu.add_separator()
        view_menu.add_checkbutton(label="Show Grid", command=self.toggle_grid)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Cut Selected", command=self.cut_selected, accelerator="Enter")
        tools_menu.add_separator()
        tools_menu.add_command(label="Performance Statistics", command=self.show_performance_stats)
        tools_menu.add_command(label="Settings", command=self.show_settings)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create the toolbar."""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
        
        # Import DXF button
        ttk.Button(self.toolbar, text="Import DXF", command=self.import_dxf).pack(side=tk.LEFT, padx=2)
        
        # Add sheet button
        ttk.Button(self.toolbar, text="Add Sheet", command=self.add_sheet).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Cut button
        ttk.Button(self.toolbar, text="Cut", command=self.cut_selected).pack(side=tk.LEFT, padx=2)
        
        # Undo button
        ttk.Button(self.toolbar, text="Undo", command=self.undo).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Export button
        ttk.Button(self.toolbar, text="Export PDF", command=self.export_pdf).pack(side=tk.LEFT, padx=2)
    
    def create_main_layout(self):
        """Create the main layout with panels."""
        # Create main paned window
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Left panel for materials and properties
        self.left_panel = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_panel, weight=1)
        
        # Right panel for canvas
        self.right_panel = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_panel, weight=3)
        
        # Create left panel components
        self.create_left_panel()
        
        # Create canvas
        self.create_canvas()
    
    def create_left_panel(self):
        """Create the left panel with materials and properties."""
        # Materials panel
        materials_frame = ttk.LabelFrame(self.left_panel, text="Materials")
        materials_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        self.materials_panel = MaterialsPanel(materials_frame, self)
        
        # Properties panel
        properties_frame = ttk.LabelFrame(self.left_panel, text="Properties")
        properties_frame.pack(fill=tk.X, padx=2, pady=2)
        
        self.properties_panel = PropertiesPanel(properties_frame, self)
    
    def create_canvas(self):
        """Create the main canvas."""
        canvas_frame = ttk.LabelFrame(self.right_panel, text="Canvas")
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        self.canvas = InfiniteCanvas(canvas_frame, self)

        # Connect snap settings from properties panel
        if hasattr(self, 'properties_panel'):
            snap_settings = self.properties_panel.get_snap_settings()
            self.canvas.drag_manager.set_snap_settings(
                snap_settings['enabled'],
                snap_settings['tolerance']
            )
    
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Status label
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Zoom label
        self.zoom_label = ttk.Label(self.status_bar, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.RIGHT, padx=5)
        
        # Snap label
        self.snap_label = ttk.Label(self.status_bar, text="Snap: ON")
        self.snap_label.pack(side=tk.RIGHT, padx=5)
    
    def bind_events(self):
        """Bind keyboard and window events."""
        # Keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self.new_project())
        self.root.bind('<Control-o>', lambda e: self.open_project())
        self.root.bind('<Control-s>', lambda e: self.save_project())
        self.root.bind('<Control-i>', lambda e: self.import_dxf())
        self.root.bind('<Control-a>', lambda e: self.add_sheet())
        self.root.bind('<Control-z>', lambda e: self.undo())
        self.root.bind('<Return>', lambda e: self.cut_selected())
        self.root.bind('<Delete>', lambda e: self.delete_selected())
        
        # Window events
        self.root.protocol("WM_DELETE_WINDOW", self.on_exit)
    
    # Menu command implementations (placeholder methods)
    def new_project(self):
        """Create a new project."""
        if self.confirm_unsaved_changes():
            self.templates.clear()
            self.sheets.clear()
            self.cuts.clear()
            self.spatial_index.clear()
            self.canvas.clear()
            self.materials_panel.refresh()
            self.project_file = None
            self.project_modified = False
            self.update_status("New project created")
    
    def open_project(self):
        """Open an existing project."""
        file_path = filedialog.askopenfilename(
            title="Open Project",
            filetypes=[
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            if self.confirm_unsaved_changes():
                self._load_project_from_file(file_path)

    def save_project(self):
        """Save the current project."""
        if self.project_file:
            self._save_project_to_file(self.project_file)
        else:
            self.save_project_as()

    def save_project_as(self):
        """Save project with new name."""
        file_path = filedialog.asksaveasfilename(
            title="Save Project As",
            defaultextension=".json",
            filetypes=[
                ("JSON files", "*.json"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self._save_project_to_file(file_path)

    def _load_project_from_file(self, file_path: str):
        """Load project from file."""
        try:
            project_data = self.project_handler.load_project(file_path)

            if project_data:
                # Clear current state
                self.templates.clear()
                self.sheets.clear()
                self.cuts.clear()
                self.spatial_index.clear()
                self.canvas.clear()

                # Load project data
                self.templates = project_data['templates']
                self.sheets = project_data['sheets']
                self.cuts = project_data['cuts']

                # Update spatial index
                for template in self.templates:
                    self.spatial_index.add_template(template)
                for sheet in self.sheets:
                    self.spatial_index.add_sheet(sheet)

                # Update canvas
                self._redraw_all_objects()

                # Update UI
                self.materials_panel.refresh()
                self.project_file = file_path
                self.project_modified = False

                # Update window title
                project_name = Path(file_path).stem
                self.root.title(f"Drywall Optimization Tool - {project_name}")

                self.update_status(f"Loaded project: {project_name}")
                logger.info(f"Successfully loaded project from {file_path}")
            else:
                messagebox.showerror("Error", "Failed to load project file")

        except Exception as e:
            logger.error(f"Failed to load project: {e}")
            messagebox.showerror("Error", f"Failed to load project:\n{str(e)}")

    def _save_project_to_file(self, file_path: str):
        """Save project to file."""
        try:
            # Get current project metrics
            project_metrics = self.metadata_tracker.get_project_metrics()

            # Prepare metadata
            metadata = {
                'project_name': Path(file_path).stem,
                'created_timestamp': datetime.now().isoformat(),
                'application_version': '1.0'
            }

            # Save project
            success = self.project_handler.save_project(
                file_path, self.templates, self.sheets, self.cuts,
                project_metrics, metadata
            )

            if success:
                self.project_file = file_path
                self.project_modified = False

                # Update window title
                project_name = Path(file_path).stem
                self.root.title(f"Drywall Optimization Tool - {project_name}")

                self.update_status(f"Saved project: {project_name}")
                logger.info(f"Successfully saved project to {file_path}")
            else:
                messagebox.showerror("Error", "Failed to save project")

        except Exception as e:
            logger.error(f"Failed to save project: {e}")
            messagebox.showerror("Error", f"Failed to save project:\n{str(e)}")

    def _redraw_all_objects(self):
        """Redraw all objects on canvas."""
        self.canvas.clear()

        # Draw sheets
        for sheet in self.sheets:
            color = 'lightblue' if not sheet.is_remainder else 'lightgreen'
            self.canvas.draw_polygon(
                sheet.polygon,
                fill=color,
                outline='blue',
                width=1,
                tags=('sheet', f'sheet_{sheet.id}')
            )

        # Draw templates
        for template in self.templates:
            self.canvas.draw_polygon(
                template.polygon,
                fill='lightyellow',
                outline='orange',
                width=2,
                tags=('template', f'template_{template.id}')
            )
    
    def import_dxf(self):
        """Import DXF file."""
        dialog = ImportDXFDialog(self.root, self)
        if dialog.result:
            # Process the import result
            imported_templates = dialog.result.get('templates', [])

            # Add templates to application state
            self.templates.extend(imported_templates)

            # Add templates to spatial index
            for template in imported_templates:
                self.spatial_index.add_template(template)

            # Draw templates on canvas
            for template in imported_templates:
                self.canvas.draw_polygon(
                    template.polygon,
                    fill='lightyellow',
                    outline='orange',
                    width=2,
                    tags=('template', f'template_{template.id}')
                )

            # Update UI
            self.project_modified = True
            self.update_status(f"Imported {len(imported_templates)} templates")

            # Zoom to fit if this is the first import
            if len(self.templates) == len(imported_templates):
                self.canvas.zoom_fit()
    
    def add_sheet(self):
        """Add a new sheet."""
        dialog = AddSheetDialog(self.root, self)
        if dialog.result:
            # Create sheet objects from dialog result
            from core.geometry.shapes import Sheet

            width = dialog.result['width']
            height = dialog.result['height']
            quantity = dialog.result['quantity']

            # Create sheets and add to inventory
            new_sheets = []
            for i in range(quantity):
                sheet = Sheet(width=width, height=height)
                new_sheets.append(sheet)
                self.sheets.append(sheet)

                # Add to spatial index
                self.spatial_index.add_sheet(sheet)

            # Update materials panel
            self.materials_panel.refresh()
            self.project_modified = True

            self.update_status(f"Added {quantity} sheets of size {width}×{height} mm")
    
    def export_pdf(self):
        """Export PDF reports."""
        if not self.templates and not self.cuts:
            messagebox.showwarning("Export Warning", "No templates or cuts to export")
            return

        file_path = filedialog.asksaveasfilename(
            title="Export PDF Report",
            defaultextension=".pdf",
            filetypes=[
                ("PDF files", "*.pdf"),
                ("All files", "*.*")
            ]
        )

        if file_path:
            self._export_pdf_report(file_path)

    def _export_pdf_report(self, file_path: str):
        """Export PDF report to file."""
        try:
            # Get current project metrics
            project_metrics = self.metadata_tracker.get_project_metrics()

            # Export comprehensive report
            success = self.pdf_exporter.export_project_report(
                file_path, self.templates, self.sheets, self.cuts, project_metrics
            )

            if success:
                self.update_status(f"Exported PDF report to {Path(file_path).name}")

                # Ask if user wants to open the PDF
                if messagebox.askyesno("Export Complete",
                                     f"PDF report exported successfully.\n\nOpen the file now?"):
                    import subprocess
                    import sys

                    if sys.platform.startswith('darwin'):  # macOS
                        subprocess.call(['open', file_path])
                    elif sys.platform.startswith('win'):  # Windows
                        subprocess.call(['start', file_path], shell=True)
                    else:  # Linux
                        subprocess.call(['xdg-open', file_path])

            else:
                messagebox.showerror("Export Error", "Failed to export PDF report")

        except Exception as e:
            logger.error(f"Failed to export PDF: {e}")
            messagebox.showerror("Export Error", f"Failed to export PDF:\n{str(e)}")

    def confirm_unsaved_changes(self) -> bool:
        """
        Confirm with user if there are unsaved changes.

        Returns:
            True if user wants to continue (save, don't save, or no changes)
        """
        if not self.project_modified:
            return True

        result = messagebox.askyesnocancel(
            "Unsaved Changes",
            "You have unsaved changes. Do you want to save before continuing?"
        )

        if result is True:  # Yes - save
            self.save_project()
            return not self.project_modified  # Continue if save was successful
        elif result is False:  # No - don't save
            return True
        else:  # Cancel
            return False
    
    def undo(self):
        """Undo last operation."""
        if not self.cutting_engine.can_undo():
            messagebox.showinfo("Undo", "No operations to undo.")
            return

        try:
            # Get the last operation
            last_operation = self.cutting_engine.undo_last_cut()

            if last_operation:
                # Remove the cut from our records
                self.cuts = [c for c in self.cuts if c.id != last_operation.cut.id]

                # Remove remainder sheets from inventory
                remainder_ids = [s.id for s in last_operation.remainder_sheets]
                self.sheets = [s for s in self.sheets if s.id not in remainder_ids]

                # Restore original sheet
                self.sheets.append(last_operation.original_sheet)

                # Update canvas
                self._update_canvas_after_undo()

                # Update UI
                self.materials_panel.refresh()
                self.project_modified = True

                self.update_status(f"Undone cut operation: {last_operation.cut.id}")
                logger.info(f"Undone cut operation: {last_operation.cut.id}")

        except Exception as e:
            logger.error(f"Undo operation failed: {e}")
            messagebox.showerror("Undo Error", f"Failed to undo: {str(e)}")

    def _update_canvas_after_undo(self):
        """Update canvas display after undo operation."""
        # Clear and redraw everything
        self.canvas.clear()

        # Redraw all sheets
        for sheet in self.sheets:
            color = 'lightblue' if not sheet.is_remainder else 'lightgreen'
            self.canvas.draw_polygon(sheet.polygon, fill=color, outline='blue', width=1)

        # Redraw all templates
        for template in self.templates:
            self.canvas.draw_polygon(template.polygon, fill='lightyellow', outline='orange', width=2)
    
    def delete_selected(self):
        """Delete selected objects."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Delete functionality will be implemented")
    
    def cut_selected(self):
        """Cut selected template from sheet."""
        with self.performance_monitor.measure('cut_selected_operation'):
            # Find overlapping template and sheet on canvas
            template, sheet = self._find_cutting_candidates()

            if not template or not sheet:
                messagebox.showwarning(
                    "No Cut Possible",
                    "Please position a template over a sheet to perform cutting."
                )
                return

            try:
                # Perform the cut with performance monitoring
                with self.performance_monitor.measure('cutting_engine_operation'):
                    result = self.cutting_engine.cut_template_from_sheet(template, sheet)

                if result.success:
                    # Record metadata
                    waste_area = sum(s.polygon.area for s in result.remainder_sheets)
                    self.metadata_tracker.record_cut(result.cut, waste_area, result.processing_time_ms)

                    # Update application state
                    self.cuts.append(result.cut)

                    # Cache objects in memory manager
                    self.memory_manager.cache_cut(result.cut)

                    # Remove original sheet from inventory
                    if sheet in self.sheets:
                        self.sheets.remove(sheet)

                    # Add remainder sheets to inventory
                    self.sheets.extend(result.remainder_sheets)

                    # Cache remainder sheets
                    for remainder in result.remainder_sheets:
                        self.memory_manager.cache_sheet(remainder)

                    # Update canvas - remove original sheet, add remainders
                    with self.performance_monitor.measure('canvas_update_after_cut'):
                        self._update_canvas_after_cut(result)

                    # Update UI
                    self.materials_panel.refresh()
                    self.project_modified = True

                    # Perform automatic memory cleanup if needed
                    self.memory_manager.auto_cleanup()

                    # Show success message
                    efficiency = result.utilization_ratio * 100
                    self.update_status(
                        f"Cut completed! Efficiency: {efficiency:.1f}%, "
                        f"Remainders: {len(result.remainder_sheets)}"
                    )

                    logger.info(f"Cut completed: {result.cut.id}")

                else:
                    messagebox.showerror("Cut Failed", result.message)

            except Exception as e:
                logger.error(f"Cut operation failed: {e}")
                messagebox.showerror("Cut Error", f"Failed to perform cut: {str(e)}")

    def _find_cutting_candidates(self):
        """
        Find template and sheet that overlap for cutting.

        Returns:
            Tuple of (template, sheet) or (None, None) if no valid pair found
        """
        # Get all objects from canvas
        canvas_objects = self.canvas.get_all_objects()

        templates_on_canvas = []
        sheets_on_canvas = []

        # Categorize objects
        for item_id, obj_data in canvas_objects.items():
            geometry = obj_data.get('geometry')
            if not geometry:
                continue

            # Try to match with our templates and sheets
            # This is a simplified approach - in a full implementation,
            # we'd have better object tracking
            for template in self.templates:
                if self._geometries_match(geometry, template.polygon):
                    templates_on_canvas.append((template, item_id, geometry))
                    break

            for sheet in self.sheets:
                if self._geometries_match(geometry, sheet.polygon):
                    sheets_on_canvas.append((sheet, item_id, geometry))
                    break

        # Find overlapping pairs
        for template, t_id, t_geom in templates_on_canvas:
            for sheet, s_id, s_geom in sheets_on_canvas:
                if t_geom.intersects(s_geom):
                    # Create updated objects with current canvas positions
                    updated_template = self._create_template_from_canvas_geometry(template, t_geom)
                    updated_sheet = self._create_sheet_from_canvas_geometry(sheet, s_geom)
                    return updated_template, updated_sheet

        return None, None

    def _geometries_match(self, geom1, geom2, tolerance=1.0):
        """Check if two geometries are approximately the same."""
        try:
            # Simple area-based matching
            area_diff = abs(geom1.area - geom2.area)
            return area_diff < tolerance
        except:
            return False

    def _create_template_from_canvas_geometry(self, original_template, canvas_geometry):
        """Create a template with updated geometry from canvas."""
        from core.geometry.shapes import Template
        return Template(
            id=original_template.id,
            polygon=canvas_geometry,
            metadata=original_template.metadata.copy()
        )

    def _create_sheet_from_canvas_geometry(self, original_sheet, canvas_geometry):
        """Create a sheet with updated geometry from canvas."""
        from core.geometry.shapes import Sheet
        return Sheet(
            id=original_sheet.id,
            polygon=canvas_geometry,
            width=original_sheet.width,
            height=original_sheet.height,
            is_remainder=original_sheet.is_remainder,
            parent_sheet_id=original_sheet.parent_sheet_id,
            metadata=original_sheet.metadata.copy()
        )

    def _update_canvas_after_cut(self, result):
        """Update canvas display after a successful cut."""
        # This is a placeholder - in a full implementation,
        # we'd have better canvas object management

        # For now, just clear and redraw everything
        # In production, we'd selectively update objects
        self.canvas.clear()

        # Redraw all remaining sheets
        for sheet in self.sheets:
            color = 'lightblue' if not sheet.is_remainder else 'lightgreen'
            self.canvas.draw_polygon(sheet.polygon, fill=color, outline='blue', width=1)

        # Redraw all templates
        for template in self.templates:
            self.canvas.draw_polygon(template.polygon, fill='lightyellow', outline='orange', width=2)
    
    def zoom_in(self):
        """Zoom in on canvas."""
        self.canvas.zoom_in()
    
    def zoom_out(self):
        """Zoom out on canvas."""
        self.canvas.zoom_out()
    
    def zoom_fit(self):
        """Fit all objects in view."""
        self.canvas.zoom_fit()
    
    def toggle_grid(self):
        """Toggle grid display."""
        self.canvas.toggle_grid()
    
    def show_settings(self):
        """Show settings dialog."""
        try:
            dialog = SettingsDialog(self.root, self.config)
            self.root.wait_window(dialog.dialog)

            if dialog.result:
                # Update configuration
                self.config.update(dialog.result)

                # Save configuration to file
                self._save_config()

                # Apply settings that can be changed immediately
                self._apply_runtime_settings()

                self.update_status("Settings updated")
                logger.info("Settings updated successfully")

        except Exception as e:
            logger.error(f"Failed to show settings dialog: {e}")
            messagebox.showerror("Settings Error", f"Failed to open settings: {str(e)}")

    def _save_config(self):
        """Save current configuration to file."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "settings.json"
            config_path.parent.mkdir(exist_ok=True)

            with open(config_path, 'w') as f:
                json.dump(self.config, f, indent=2)

            logger.info(f"Configuration saved to {config_path}")

        except Exception as e:
            logger.warning(f"Failed to save configuration: {e}")

    def _apply_runtime_settings(self):
        """Apply settings that can be changed at runtime."""
        try:
            # Update canvas settings
            canvas_config = self.config.get('canvas', {})

            if hasattr(self.canvas, 'set_grid_spacing'):
                self.canvas.set_grid_spacing(canvas_config.get('grid_spacing', 50))

            if hasattr(self.canvas, 'set_grid_color'):
                self.canvas.set_grid_color(canvas_config.get('grid_color', '#d0d0d0'))

            if hasattr(self.canvas, 'toggle_grid'):
                show_grid = canvas_config.get('show_grid', True)
                if show_grid != getattr(self.canvas, 'grid_visible', True):
                    self.canvas.toggle_grid()

            # Update snapping settings
            snap_config = self.config.get('snapping', {})
            if hasattr(self.properties_panel, 'update_snap_settings'):
                self.properties_panel.update_snap_settings(snap_config)

            logger.debug("Runtime settings applied")

        except Exception as e:
            logger.warning(f"Failed to apply some runtime settings: {e}")

    def show_performance_stats(self):
        """Show performance statistics dialog."""
        try:
            dialog = PerformanceStatsDialog(self.root, self.performance_monitor, self.memory_manager)
            # Dialog is non-modal, so we don't wait for it

        except Exception as e:
            logger.error(f"Failed to show performance stats dialog: {e}")
            messagebox.showerror("Performance Stats Error", f"Failed to open performance statistics: {str(e)}")

    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo("About", "Drywall Optimization Tool v1.0\nDeveloped by GÖDE Team")
    
    def update_status(self, message: str):
        """Update status bar message."""
        self.status_label.config(text=message)
        logger.info(f"Status: {message}")
    
    def confirm_unsaved_changes(self) -> bool:
        """Confirm if user wants to discard unsaved changes."""
        if self.project_modified:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before continuing?"
            )
            if result is True:  # Yes - save
                return self.save_project()
            elif result is False:  # No - discard
                return True
            else:  # Cancel
                return False
        return True
    
    def confirm_exit(self) -> bool:
        """Confirm application exit."""
        return self.confirm_unsaved_changes()
    
    def on_exit(self):
        """Handle application exit."""
        if self.confirm_exit():
            self.root.quit()
