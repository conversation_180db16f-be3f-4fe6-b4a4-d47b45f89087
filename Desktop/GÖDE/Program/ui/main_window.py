"""
Main application window for the drywall optimization tool.
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import logging
from pathlib import Path
import json
from typing import Optional, Dict, Any

from .canvas.infinite_canvas import InfiniteCanvas
from .panels.materials import MaterialsPanel
from .panels.properties import PropertiesPanel
from .dialogs.import_dxf import ImportDXFDialog
from .dialogs.add_sheet import AddSheetDialog

from core.dxf_import.converter import DXFConverter
from core.geometry.spatial import SpatialIndex
from utils.validation import validate_file_path


logger = logging.getLogger(__name__)


class MainWindow:
    """Main application window."""
    
    def __init__(self, root: tk.Tk):
        """
        Initialize main window.
        
        Args:
            root: Tkinter root window
        """
        self.root = root
        self.setup_window()
        
        # Initialize components
        self.dxf_converter = DXFConverter()
        self.spatial_index = SpatialIndex()
        
        # Application state
        self.project_file = None
        self.project_modified = False
        self.templates = []
        self.sheets = []
        self.cuts = []
        
        # Load configuration
        self.config = self.load_config()
        
        # Create UI
        self.create_menu()
        self.create_toolbar()
        self.create_main_layout()
        self.create_status_bar()
        
        # Bind events
        self.bind_events()
        
        logger.info("Main window initialized")
    
    def setup_window(self):
        """Configure the main window."""
        self.root.title("Drywall Optimization Tool")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Configure window icon (if available)
        try:
            # You can add an icon file later
            pass
        except:
            pass
    
    def load_config(self) -> Dict[str, Any]:
        """Load application configuration."""
        try:
            config_path = Path(__file__).parent.parent / "config" / "settings.json"
            with open(config_path, 'r') as f:
                return json.load(f)
        except Exception as e:
            logger.warning(f"Failed to load config: {e}")
            return {}
    
    def create_menu(self):
        """Create the menu bar."""
        self.menubar = tk.Menu(self.root)
        self.root.config(menu=self.menubar)
        
        # File menu
        file_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="File", menu=file_menu)
        file_menu.add_command(label="New Project", command=self.new_project, accelerator="Ctrl+N")
        file_menu.add_command(label="Open Project", command=self.open_project, accelerator="Ctrl+O")
        file_menu.add_command(label="Save Project", command=self.save_project, accelerator="Ctrl+S")
        file_menu.add_command(label="Save As...", command=self.save_project_as, accelerator="Ctrl+Shift+S")
        file_menu.add_separator()
        file_menu.add_command(label="Import DXF", command=self.import_dxf, accelerator="Ctrl+I")
        file_menu.add_separator()
        file_menu.add_command(label="Export PDF", command=self.export_pdf, accelerator="Ctrl+E")
        file_menu.add_separator()
        file_menu.add_command(label="Exit", command=self.on_exit, accelerator="Ctrl+Q")
        
        # Edit menu
        edit_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Edit", menu=edit_menu)
        edit_menu.add_command(label="Undo", command=self.undo, accelerator="Ctrl+Z")
        edit_menu.add_separator()
        edit_menu.add_command(label="Add Sheet", command=self.add_sheet, accelerator="Ctrl+A")
        edit_menu.add_command(label="Delete Selected", command=self.delete_selected, accelerator="Delete")
        
        # View menu
        view_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="View", menu=view_menu)
        view_menu.add_command(label="Zoom In", command=self.zoom_in, accelerator="Ctrl++")
        view_menu.add_command(label="Zoom Out", command=self.zoom_out, accelerator="Ctrl+-")
        view_menu.add_command(label="Zoom Fit", command=self.zoom_fit, accelerator="Ctrl+0")
        view_menu.add_separator()
        view_menu.add_checkbutton(label="Show Grid", command=self.toggle_grid)
        
        # Tools menu
        tools_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Tools", menu=tools_menu)
        tools_menu.add_command(label="Cut Selected", command=self.cut_selected, accelerator="Enter")
        tools_menu.add_separator()
        tools_menu.add_command(label="Settings", command=self.show_settings)
        
        # Help menu
        help_menu = tk.Menu(self.menubar, tearoff=0)
        self.menubar.add_cascade(label="Help", menu=help_menu)
        help_menu.add_command(label="About", command=self.show_about)
    
    def create_toolbar(self):
        """Create the toolbar."""
        self.toolbar = ttk.Frame(self.root)
        self.toolbar.pack(side=tk.TOP, fill=tk.X, padx=2, pady=2)
        
        # Import DXF button
        ttk.Button(self.toolbar, text="Import DXF", command=self.import_dxf).pack(side=tk.LEFT, padx=2)
        
        # Add sheet button
        ttk.Button(self.toolbar, text="Add Sheet", command=self.add_sheet).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Cut button
        ttk.Button(self.toolbar, text="Cut", command=self.cut_selected).pack(side=tk.LEFT, padx=2)
        
        # Undo button
        ttk.Button(self.toolbar, text="Undo", command=self.undo).pack(side=tk.LEFT, padx=2)
        
        # Separator
        ttk.Separator(self.toolbar, orient=tk.VERTICAL).pack(side=tk.LEFT, fill=tk.Y, padx=5)
        
        # Export button
        ttk.Button(self.toolbar, text="Export PDF", command=self.export_pdf).pack(side=tk.LEFT, padx=2)
    
    def create_main_layout(self):
        """Create the main layout with panels."""
        # Create main paned window
        self.main_paned = ttk.PanedWindow(self.root, orient=tk.HORIZONTAL)
        self.main_paned.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        # Left panel for materials and properties
        self.left_panel = ttk.Frame(self.main_paned)
        self.main_paned.add(self.left_panel, weight=1)
        
        # Right panel for canvas
        self.right_panel = ttk.Frame(self.main_paned)
        self.main_paned.add(self.right_panel, weight=3)
        
        # Create left panel components
        self.create_left_panel()
        
        # Create canvas
        self.create_canvas()
    
    def create_left_panel(self):
        """Create the left panel with materials and properties."""
        # Materials panel
        materials_frame = ttk.LabelFrame(self.left_panel, text="Materials")
        materials_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)
        
        self.materials_panel = MaterialsPanel(materials_frame, self)
        
        # Properties panel
        properties_frame = ttk.LabelFrame(self.left_panel, text="Properties")
        properties_frame.pack(fill=tk.X, padx=2, pady=2)
        
        self.properties_panel = PropertiesPanel(properties_frame, self)
    
    def create_canvas(self):
        """Create the main canvas."""
        canvas_frame = ttk.LabelFrame(self.right_panel, text="Canvas")
        canvas_frame.pack(fill=tk.BOTH, expand=True, padx=2, pady=2)

        self.canvas = InfiniteCanvas(canvas_frame, self)

        # Connect snap settings from properties panel
        if hasattr(self, 'properties_panel'):
            snap_settings = self.properties_panel.get_snap_settings()
            self.canvas.drag_manager.set_snap_settings(
                snap_settings['enabled'],
                snap_settings['tolerance']
            )
    
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(side=tk.BOTTOM, fill=tk.X)
        
        # Status label
        self.status_label = ttk.Label(self.status_bar, text="Ready")
        self.status_label.pack(side=tk.LEFT, padx=5)
        
        # Zoom label
        self.zoom_label = ttk.Label(self.status_bar, text="Zoom: 100%")
        self.zoom_label.pack(side=tk.RIGHT, padx=5)
        
        # Snap label
        self.snap_label = ttk.Label(self.status_bar, text="Snap: ON")
        self.snap_label.pack(side=tk.RIGHT, padx=5)
    
    def bind_events(self):
        """Bind keyboard and window events."""
        # Keyboard shortcuts
        self.root.bind('<Control-n>', lambda e: self.new_project())
        self.root.bind('<Control-o>', lambda e: self.open_project())
        self.root.bind('<Control-s>', lambda e: self.save_project())
        self.root.bind('<Control-i>', lambda e: self.import_dxf())
        self.root.bind('<Control-a>', lambda e: self.add_sheet())
        self.root.bind('<Control-z>', lambda e: self.undo())
        self.root.bind('<Return>', lambda e: self.cut_selected())
        self.root.bind('<Delete>', lambda e: self.delete_selected())
        
        # Window events
        self.root.protocol("WM_DELETE_WINDOW", self.on_exit)
    
    # Menu command implementations (placeholder methods)
    def new_project(self):
        """Create a new project."""
        if self.confirm_unsaved_changes():
            self.templates.clear()
            self.sheets.clear()
            self.cuts.clear()
            self.spatial_index.clear()
            self.canvas.clear()
            self.materials_panel.refresh()
            self.project_file = None
            self.project_modified = False
            self.update_status("New project created")
    
    def open_project(self):
        """Open an existing project."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Open project functionality will be implemented")
    
    def save_project(self):
        """Save the current project."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Save project functionality will be implemented")
    
    def save_project_as(self):
        """Save project with new name."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Save as functionality will be implemented")
    
    def import_dxf(self):
        """Import DXF file."""
        dialog = ImportDXFDialog(self.root, self)
        if dialog.result:
            # Process the import result
            self.update_status(f"Imported {len(dialog.result.get('templates', []))} templates")
    
    def add_sheet(self):
        """Add a new sheet."""
        dialog = AddSheetDialog(self.root, self)
        if dialog.result:
            # Process the new sheet
            self.update_status("Sheet added")
    
    def export_pdf(self):
        """Export PDF reports."""
        # Implementation will be added later
        messagebox.showinfo("Info", "PDF export functionality will be implemented")
    
    def undo(self):
        """Undo last operation."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Undo functionality will be implemented")
    
    def delete_selected(self):
        """Delete selected objects."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Delete functionality will be implemented")
    
    def cut_selected(self):
        """Cut selected template from sheet."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Cut functionality will be implemented")
    
    def zoom_in(self):
        """Zoom in on canvas."""
        self.canvas.zoom_in()
    
    def zoom_out(self):
        """Zoom out on canvas."""
        self.canvas.zoom_out()
    
    def zoom_fit(self):
        """Fit all objects in view."""
        self.canvas.zoom_fit()
    
    def toggle_grid(self):
        """Toggle grid display."""
        self.canvas.toggle_grid()
    
    def show_settings(self):
        """Show settings dialog."""
        # Implementation will be added later
        messagebox.showinfo("Info", "Settings dialog will be implemented")
    
    def show_about(self):
        """Show about dialog."""
        messagebox.showinfo("About", "Drywall Optimization Tool v1.0\nDeveloped by GÖDE Team")
    
    def update_status(self, message: str):
        """Update status bar message."""
        self.status_label.config(text=message)
        logger.info(f"Status: {message}")
    
    def confirm_unsaved_changes(self) -> bool:
        """Confirm if user wants to discard unsaved changes."""
        if self.project_modified:
            result = messagebox.askyesnocancel(
                "Unsaved Changes",
                "You have unsaved changes. Do you want to save before continuing?"
            )
            if result is True:  # Yes - save
                return self.save_project()
            elif result is False:  # No - discard
                return True
            else:  # Cancel
                return False
        return True
    
    def confirm_exit(self) -> bool:
        """Confirm application exit."""
        return self.confirm_unsaved_changes()
    
    def on_exit(self):
        """Handle application exit."""
        if self.confirm_exit():
            self.root.quit()
