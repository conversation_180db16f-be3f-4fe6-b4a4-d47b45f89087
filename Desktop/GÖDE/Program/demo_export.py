#!/usr/bin/env python3
"""
Demo script for testing export and persistence functionality.

This script demonstrates:
1. Creating a sample project with templates, sheets, and cuts
2. Saving project to JSON file
3. Loading project from JSON file
4. Exporting PDF reports
5. Project backup and recovery
"""

import sys
import json
from pathlib import Path
import logging

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(levelname)s: %(message)s')
logger = logging.getLogger(__name__)

# Import our modules
from core.geometry.shapes import Template, Sheet, Cut, Point, LineSegment
from core.cutting.engine import CuttingEngine
from core.cutting.metadata import MetadataTracker, ProjectMetrics
from export.pdf import PDFExporter
from persistence import JSONProjectHandler, ProjectSerializer
from shapely.geometry import box, Polygon
import time


def create_sample_project():
    """Create a sample project with templates, sheets, and cuts."""
    logger.info("Creating sample project...")
    
    # Create templates
    templates = []
    
    # Rectangle template
    rect_polygon = box(0, 0, 200, 100)
    rect_template = Template(
        polygon=rect_polygon,
        metadata={'name': 'Rectangle Template', 'type': 'standard'}
    )
    templates.append(rect_template)
    
    # L-shaped template
    l_coords = [(0, 0), (150, 0), (150, 75), (75, 75), (75, 150), (0, 150), (0, 0)]
    l_polygon = Polygon(l_coords)
    l_template = Template(
        polygon=l_polygon,
        metadata={'name': 'L-Shape Template', 'type': 'complex'}
    )
    templates.append(l_template)
    
    # Create sheets
    sheets = []
    
    # Standard sheet
    standard_sheet = Sheet(
        width=1200, height=2400,
        metadata={'name': 'Standard Sheet', 'material': 'Drywall 12.5mm'}
    )
    sheets.append(standard_sheet)
    
    # Half sheet
    half_sheet = Sheet(
        width=600, height=1200,
        metadata={'name': 'Half Sheet', 'material': 'Drywall 12.5mm'}
    )
    sheets.append(half_sheet)
    
    # Create cuts using cutting engine
    cutting_engine = CuttingEngine()
    cuts = []
    
    # Cut rectangle from standard sheet
    positioned_rect = rect_template.move_to(Point(100, 100))
    result1 = cutting_engine.cut_template_from_sheet(positioned_rect, standard_sheet)
    
    if result1.success:
        cuts.append(result1.cut)
        # Add remainder sheets
        sheets.extend(result1.remainder_sheets)
    
    # Cut L-shape from half sheet
    positioned_l = l_template.move_to(Point(50, 50))
    result2 = cutting_engine.cut_template_from_sheet(positioned_l, half_sheet)
    
    if result2.success:
        cuts.append(result2.cut)
        sheets.extend(result2.remainder_sheets)
    
    logger.info(f"Created sample project: {len(templates)} templates, {len(sheets)} sheets, {len(cuts)} cuts")
    
    return templates, sheets, cuts


def test_project_serialization():
    """Test project serialization and deserialization."""
    logger.info("Testing project serialization...")
    
    try:
        # Create sample project
        templates, sheets, cuts = create_sample_project()
        
        # Create project metrics
        project_metrics = ProjectMetrics(
            total_cuts=len(cuts),
            total_cut_area_mm2=sum(cut.cut_area for cut in cuts),
            total_waste_area_mm2=1000.0,
            average_utilization=0.75,
            average_efficiency=80.0,
            sheets_used=2,
            templates_cut=2
        )
        
        # Test serialization
        serializer = ProjectSerializer()
        
        project_data = serializer.serialize_project(
            templates, sheets, cuts, project_metrics,
            metadata={'test': True, 'created_by': 'demo'}
        )
        
        logger.info("✓ Project serialization successful")
        
        # Test deserialization
        restored_data = serializer.deserialize_project(project_data)
        
        logger.info("✓ Project deserialization successful")
        logger.info(f"  Restored: {len(restored_data['templates'])} templates, "
                   f"{len(restored_data['sheets'])} sheets, {len(restored_data['cuts'])} cuts")
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Project serialization test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_json_persistence():
    """Test JSON file save/load functionality."""
    logger.info("Testing JSON persistence...")
    
    try:
        # Create sample project
        templates, sheets, cuts = create_sample_project()
        
        # Create metadata tracker and add some metrics
        metadata_tracker = MetadataTracker()
        for cut in cuts:
            waste_area = 500.0  # Mock waste area
            metadata_tracker.record_cut(cut, waste_area, 50.0)  # 50ms processing time
        
        project_metrics = metadata_tracker.get_project_metrics()
        
        # Test save
        handler = JSONProjectHandler()
        test_file = project_root / "test_project.json"
        
        success = handler.save_project(
            str(test_file), templates, sheets, cuts, project_metrics,
            metadata={'test_project': True, 'demo_version': '1.0'}
        )
        
        if not success:
            logger.error("✗ Project save failed")
            return False
        
        logger.info("✓ Project save successful")
        
        # Test load
        loaded_data = handler.load_project(str(test_file))
        
        if not loaded_data:
            logger.error("✗ Project load failed")
            return False
        
        logger.info("✓ Project load successful")
        logger.info(f"  Loaded: {len(loaded_data['templates'])} templates, "
                   f"{len(loaded_data['sheets'])} sheets, {len(loaded_data['cuts'])} cuts")
        
        # Test backup listing
        backups = handler.list_backups(str(test_file))
        logger.info(f"✓ Found {len(backups)} backup files")
        
        # Clean up
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ JSON persistence test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_pdf_export():
    """Test PDF export functionality."""
    logger.info("Testing PDF export...")
    
    try:
        # Create sample project
        templates, sheets, cuts = create_sample_project()
        
        # Create project metrics
        project_metrics = ProjectMetrics(
            total_cuts=len(cuts),
            total_cut_area_mm2=sum(cut.cut_area for cut in cuts),
            total_waste_area_mm2=2000.0,
            total_cut_length_mm=sum(cut.total_cut_length for cut in cuts),
            average_utilization=0.75,
            average_efficiency=82.5,
            sheets_used=len([s for s in sheets if not s.is_remainder]),
            templates_cut=len(templates)
        )
        
        # Test PDF export
        exporter = PDFExporter()
        test_pdf = project_root / "test_report.pdf"
        
        success = exporter.export_project_report(
            str(test_pdf), templates, sheets, cuts, project_metrics
        )
        
        if not success:
            logger.error("✗ PDF export failed")
            return False
        
        logger.info("✓ PDF export successful")
        
        # Check file size
        if test_pdf.exists():
            file_size = test_pdf.stat().st_size
            logger.info(f"  PDF file size: {file_size} bytes")
            
            # Clean up
            test_pdf.unlink()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ PDF export test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_project_summary():
    """Test project summary export."""
    logger.info("Testing project summary export...")
    
    try:
        # Create and save a test project
        templates, sheets, cuts = create_sample_project()
        
        metadata_tracker = MetadataTracker()
        for cut in cuts:
            metadata_tracker.record_cut(cut, 300.0, 25.0)
        
        project_metrics = metadata_tracker.get_project_metrics()
        
        handler = JSONProjectHandler()
        test_file = project_root / "summary_test_project.json"
        
        # Save project
        handler.save_project(
            str(test_file), templates, sheets, cuts, project_metrics,
            metadata={'summary_test': True}
        )
        
        # Export summary
        summary = handler.export_project_summary(str(test_file))
        
        if not summary:
            logger.error("✗ Project summary export failed")
            return False
        
        logger.info("✓ Project summary export successful")
        logger.info(f"  Summary contains: {summary['statistics']}")
        
        # Clean up
        if test_file.exists():
            test_file.unlink()
        
        return True
        
    except Exception as e:
        logger.error(f"✗ Project summary test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all export and persistence tests."""
    print("=== Export and Persistence Tests ===\n")
    
    tests = [
        test_project_serialization,
        test_json_persistence,
        test_pdf_export,
        test_project_summary
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
            print()  # Add spacing between tests
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
            print()
    
    print(f"=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All export and persistence tests passed!")
        print("\nPhase 3 implementation is complete:")
        print("- ✅ Project serialization with WKT geometry")
        print("- ✅ JSON file save/load with backup support")
        print("- ✅ PDF report generation with visual layouts")
        print("- ✅ Project summary and metadata export")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
