#!/usr/bin/env python3
"""
Basic test script to verify the project structure and core functionality.
"""

import sys
import logging
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_imports():
    """Test basic imports."""
    print("Testing imports...")
    
    try:
        import tkinter as tk
        print("✓ tkinter available")
    except ImportError as e:
        print(f"✗ tkinter not available: {e}")
        return False
    
    try:
        from utils.validation import validate_numeric_input
        from utils.units import UnitConverter
        print("✓ utils modules imported")
    except ImportError as e:
        print(f"✗ utils import failed: {e}")
        return False
    
    try:
        from core.geometry.shapes import Point, Template, Sheet, Cut
        from core.geometry.operations import GeometryOperations
        print("✓ core.geometry modules imported")
    except ImportError as e:
        print(f"✗ core.geometry import failed: {e}")
        return False
    
    return True

def test_geometry():
    """Test basic geometry functionality."""
    print("\nTesting geometry...")
    
    try:
        from core.geometry.shapes import Point, Template, Sheet
        from shapely.geometry import Polygon
        
        # Test Point
        p1 = Point(0, 0)
        p2 = Point(10, 10)
        distance = p1.distance_to(p2)
        print(f"✓ Point distance calculation: {distance:.2f}")
        
        # Test simple polygon
        coords = [(0, 0), (100, 0), (100, 200), (0, 200), (0, 0)]
        polygon = Polygon(coords)
        
        # Test Template
        template = Template(polygon=polygon)
        print(f"✓ Template created with area: {template.area}")
        
        # Test Sheet
        sheet = Sheet(width=1200, height=2400)
        print(f"✓ Sheet created with area: {sheet.area}")
        
        return True
        
    except Exception as e:
        print(f"✗ Geometry test failed: {e}")
        return False

def test_units():
    """Test unit conversion."""
    print("\nTesting unit conversion...")
    
    try:
        from utils.units import UnitConverter
        
        # Test conversion
        mm_value = 1200
        cm_value = UnitConverter.convert(mm_value, 'mm', 'cm')
        print(f"✓ {mm_value} mm = {cm_value} cm")
        
        # Test formatting
        formatted = UnitConverter.format_value(1200, 'mm', 1)
        print(f"✓ Formatted value: {formatted}")
        
        return True
        
    except Exception as e:
        print(f"✗ Unit conversion test failed: {e}")
        return False

def test_validation():
    """Test input validation."""
    print("\nTesting validation...")
    
    try:
        from utils.validation import validate_numeric_input, validate_sheet_dimensions
        
        # Test numeric validation
        is_valid, value = validate_numeric_input("123.45", min_value=0, max_value=1000)
        print(f"✓ Numeric validation: {is_valid}, value: {value}")
        
        # Test sheet dimensions
        is_valid, width, height = validate_sheet_dimensions("1200", "2400")
        print(f"✓ Sheet dimensions validation: {is_valid}, {width}x{height}")
        
        return True
        
    except Exception as e:
        print(f"✗ Validation test failed: {e}")
        return False

def test_gui_basic():
    """Test basic GUI creation."""
    print("\nTesting basic GUI...")
    
    try:
        import tkinter as tk
        
        # Create a simple window
        root = tk.Tk()
        root.title("Test Window")
        root.geometry("300x200")
        root.withdraw()  # Hide the window
        
        # Test basic widgets
        frame = tk.Frame(root)
        label = tk.Label(frame, text="Test Label")
        button = tk.Button(frame, text="Test Button")
        
        print("✓ Basic GUI widgets created successfully")
        
        # Clean up
        root.destroy()
        
        return True
        
    except Exception as e:
        print(f"✗ GUI test failed: {e}")
        return False

def main():
    """Run all tests."""
    print("=== Drywall Optimizer - Basic Tests ===\n")
    
    tests = [
        test_imports,
        test_geometry,
        test_units,
        test_validation,
        test_gui_basic
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"✗ Test {test.__name__} crashed: {e}")
    
    print(f"\n=== Test Results ===")
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed! The basic structure is working correctly.")
        return 0
    else:
        print("⚠️  Some tests failed. Check the output above for details.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
