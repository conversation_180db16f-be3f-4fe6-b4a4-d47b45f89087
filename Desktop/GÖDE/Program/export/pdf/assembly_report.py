"""
Assembly report generator for template installation instructions.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import math

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.lib.colors import black, blue, red, green, gray
from reportlab.lib.colors import Color

# Define lightgray if not available
try:
    from reportlab.lib.colors import lightgray
except ImportError:
    lightgray = Color(0.9, 0.9, 0.9)
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import Paragraph, Table, TableStyle
from shapely.geometry import Polygon

from core.geometry.shapes import Template, Cut, Sheet
from utils.units import UnitConverter

logger = logging.getLogger(__name__)


class AssemblyReportGenerator:
    """
    Generates assembly reports showing how to install cut templates.
    
    Features:
    - Template layout diagrams
    - Installation sequence
    - Piece origin tracking
    - Assembly instructions
    - Material requirements
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize assembly report generator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Drawing settings
        self.margin = 15 * mm
        self.line_width = 0.5
        self.font_size = 9
        self.title_font_size = 12
        
        # Colors
        self.template_color = lightgray
        self.piece_colors = [blue, green, red, gray]  # Cycle through for different pieces
        self.instruction_color = black
        
        logger.info("AssemblyReportGenerator initialized")
    
    def generate_assembly_report(self, pdf_canvas: canvas.Canvas, template: Template,
                               cuts: List[Cut], sheets: List[Sheet],
                               x_offset: float = 0, y_offset: float = 0,
                               max_width: float = None, max_height: float = None) -> Tuple[float, float]:
        """
        Generate assembly report for a template.
        
        Args:
            pdf_canvas: ReportLab canvas to draw on
            template: Template to create assembly report for
            cuts: List of cuts that created pieces of this template
            sheets: List of sheets for reference
            x_offset, y_offset: Position offset on canvas
            max_width, max_height: Maximum dimensions for layout
            
        Returns:
            Tuple of (actual_width, actual_height) used
        """
        try:
            if max_width is None:
                max_width = pdf_canvas._pagesize[0] - 2 * self.margin
            if max_height is None:
                max_height = pdf_canvas._pagesize[1] - 2 * self.margin
            
            current_y = y_offset
            
            # Draw title
            title_height = self._draw_assembly_title(pdf_canvas, template, x_offset, current_y)
            current_y += title_height + 10
            
            # Draw template overview
            overview_height = self._draw_template_overview(pdf_canvas, template, cuts,
                                                         x_offset, current_y, max_width)
            current_y += overview_height + 15
            
            # Draw piece breakdown
            breakdown_height = self._draw_piece_breakdown(pdf_canvas, template, cuts, sheets,
                                                        x_offset, current_y, max_width)
            current_y += breakdown_height + 15
            
            # Draw assembly instructions
            instructions_height = self._draw_assembly_instructions(pdf_canvas, template, cuts,
                                                                 x_offset, current_y, max_width)
            current_y += instructions_height
            
            total_height = current_y - y_offset
            
            return max_width, total_height
            
        except Exception as e:
            logger.error(f"Failed to generate assembly report: {e}")
            return 0, 0
    
    def _draw_assembly_title(self, pdf_canvas: canvas.Canvas, template: Template,
                           x_offset: float, y_offset: float) -> float:
        """Draw assembly report title."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.title_font_size)
        
        template_name = template.metadata.get('name', f'Template {template.id[:8]}')
        title_text = f"Assembly Report: {template_name}"
        
        pdf_canvas.drawString(x_offset, y_offset, title_text)
        
        # Draw template info
        pdf_canvas.setFont("Helvetica", self.font_size)
        info_text = f"Template ID: {template.id[:8]}... | Area: {template.area:.0f} mm²"
        pdf_canvas.drawString(x_offset, y_offset - 15, info_text)
        
        return 30
    
    def _draw_template_overview(self, pdf_canvas: canvas.Canvas, template: Template,
                              cuts: List[Cut], x_offset: float, y_offset: float,
                              max_width: float) -> float:
        """Draw template overview diagram."""
        # Calculate scale for template
        template_bounds = template.polygon.bounds
        template_width = template_bounds[2] - template_bounds[0]
        template_height = template_bounds[3] - template_bounds[1]
        
        available_width = max_width * 0.6  # Use 60% of available width
        available_height = 150  # Fixed height for overview
        
        scale_x = available_width / template_width if template_width > 0 else 1
        scale_y = available_height / template_height if template_height > 0 else 1
        scale = min(scale_x, scale_y, 2.0)  # Max 2x scale
        
        scaled_width = template_width * scale
        scaled_height = template_height * scale
        
        # Draw template outline
        self._draw_template_shape(pdf_canvas, template, x_offset, y_offset, scale)
        
        # Draw piece divisions
        self._draw_piece_divisions(pdf_canvas, template, cuts, x_offset, y_offset, scale)
        
        # Draw legend
        legend_x = x_offset + scaled_width + 20
        self._draw_piece_legend(pdf_canvas, cuts, legend_x, y_offset, max_width - scaled_width - 20)
        
        return max(scaled_height, 100)
    
    def _draw_template_shape(self, pdf_canvas: canvas.Canvas, template: Template,
                           x_offset: float, y_offset: float, scale: float):
        """Draw the template shape outline."""
        try:
            coords = list(template.polygon.exterior.coords)
            
            if len(coords) < 3:
                return
            
            # Scale and offset coordinates
            scaled_coords = []
            bounds = template.polygon.bounds
            min_x, min_y = bounds[0], bounds[1]
            
            for coord_x, coord_y in coords:
                scaled_x = x_offset + (coord_x - min_x) * scale
                scaled_y = y_offset + (coord_y - min_y) * scale
                scaled_coords.append((scaled_x, scaled_y))
            
            # Draw template outline
            pdf_canvas.setStrokeColor(black)
            pdf_canvas.setFillColor(self.template_color)
            pdf_canvas.setLineWidth(self.line_width * 2)
            
            path = pdf_canvas.beginPath()
            path.moveTo(scaled_coords[0][0], scaled_coords[0][1])
            
            for coord_x, coord_y in scaled_coords[1:]:
                path.lineTo(coord_x, coord_y)
            
            path.close()
            pdf_canvas.drawPath(path, fill=1, stroke=1)
            
        except Exception as e:
            logger.warning(f"Failed to draw template shape: {e}")
    
    def _draw_piece_divisions(self, pdf_canvas: canvas.Canvas, template: Template,
                            cuts: List[Cut], x_offset: float, y_offset: float, scale: float):
        """Draw divisions showing different pieces."""
        bounds = template.polygon.bounds
        min_x, min_y = bounds[0], bounds[1]
        
        for i, cut in enumerate(cuts):
            try:
                # Get piece color
                color = self.piece_colors[i % len(self.piece_colors)]
                
                # Draw cut polygon
                coords = list(cut.cut_polygon.exterior.coords)
                scaled_coords = []
                
                for coord_x, coord_y in coords:
                    scaled_x = x_offset + (coord_x - min_x) * scale
                    scaled_y = y_offset + (coord_y - min_y) * scale
                    scaled_coords.append((scaled_x, scaled_y))
                
                if len(scaled_coords) >= 3:
                    pdf_canvas.setStrokeColor(color)
                    pdf_canvas.setFillColor(color)
                    pdf_canvas.setLineWidth(self.line_width)
                    
                    path = pdf_canvas.beginPath()
                    path.moveTo(scaled_coords[0][0], scaled_coords[0][1])
                    
                    for coord_x, coord_y in scaled_coords[1:]:
                        path.lineTo(coord_x, coord_y)
                    
                    path.close()
                    pdf_canvas.drawPath(path, fill=0, stroke=1)
                
                # Draw piece number
                centroid = cut.cut_polygon.centroid
                label_x = x_offset + (centroid.x - min_x) * scale
                label_y = y_offset + (centroid.y - min_y) * scale
                
                pdf_canvas.setFillColor(color)
                pdf_canvas.setFont("Helvetica-Bold", self.font_size)
                piece_label = str(i + 1)
                text_width = pdf_canvas.stringWidth(piece_label, "Helvetica-Bold", self.font_size)
                pdf_canvas.drawString(label_x - text_width/2, label_y, piece_label)
                
            except Exception as e:
                logger.warning(f"Failed to draw piece division for cut {cut.id}: {e}")
    
    def _draw_piece_legend(self, pdf_canvas: canvas.Canvas, cuts: List[Cut],
                         x_offset: float, y_offset: float, max_width: float):
        """Draw legend showing piece information."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size)
        pdf_canvas.drawString(x_offset, y_offset, "Pieces:")
        
        current_y = y_offset - 15
        
        for i, cut in enumerate(cuts):
            if current_y < y_offset - 100:  # Limit legend height
                break
            
            color = self.piece_colors[i % len(self.piece_colors)]
            
            # Draw color indicator
            pdf_canvas.setFillColor(color)
            pdf_canvas.setStrokeColor(color)
            pdf_canvas.rect(x_offset, current_y, 8, 8, fill=1, stroke=1)
            
            # Draw piece info
            pdf_canvas.setFillColor(black)
            pdf_canvas.setFont("Helvetica", self.font_size - 1)
            
            piece_text = f"{i+1}. Sheet: {cut.sheet_id[:6]}..."
            pdf_canvas.drawString(x_offset + 12, current_y + 1, piece_text)
            
            current_y -= 12
    
    def _draw_piece_breakdown(self, pdf_canvas: canvas.Canvas, template: Template,
                            cuts: List[Cut], sheets: List[Sheet],
                            x_offset: float, y_offset: float, max_width: float) -> float:
        """Draw detailed piece breakdown table."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size + 1)
        pdf_canvas.drawString(x_offset, y_offset, "Piece Breakdown:")
        
        current_y = y_offset - 20
        
        # Table headers
        headers = ["Piece", "Sheet ID", "Area (mm²)", "Cut Length (mm)", "Origin"]
        col_widths = [40, 80, 70, 80, 100]
        
        # Draw headers
        pdf_canvas.setFillColor(gray)
        header_height = 15
        current_x = x_offset
        
        for i, (header, width) in enumerate(zip(headers, col_widths)):
            pdf_canvas.rect(current_x, current_y - header_height, width, header_height, fill=1, stroke=1)
            
            pdf_canvas.setFillColor(black)
            pdf_canvas.setFont("Helvetica-Bold", self.font_size - 1)
            text_width = pdf_canvas.stringWidth(header, "Helvetica-Bold", self.font_size - 1)
            pdf_canvas.drawString(current_x + (width - text_width) / 2, 
                                current_y - header_height + 3, header)
            
            current_x += width
        
        current_y -= header_height
        
        # Draw data rows
        pdf_canvas.setFont("Helvetica", self.font_size - 1)
        row_height = 12
        
        for i, cut in enumerate(cuts):
            if i >= 10:  # Limit to 10 rows
                break
            
            # Find source sheet
            source_sheet = None
            for sheet in sheets:
                if sheet.id == cut.sheet_id:
                    source_sheet = sheet
                    break
            
            origin = "Unknown"
            if source_sheet:
                if source_sheet.is_remainder:
                    origin = "Remainder"
                else:
                    origin = "New Sheet"
            
            row_data = [
                str(i + 1),
                cut.sheet_id[:8] + "...",
                f"{cut.cut_area:.0f}",
                f"{cut.total_cut_length:.0f}",
                origin
            ]
            
            current_x = x_offset
            
            # Alternate row colors
            if i % 2 == 1:
                pdf_canvas.setFillColor(lightgray)
                pdf_canvas.rect(current_x, current_y - row_height, sum(col_widths), row_height, fill=1, stroke=0)
            
            pdf_canvas.setFillColor(black)
            
            for data, width in zip(row_data, col_widths):
                pdf_canvas.drawString(current_x + 2, current_y - row_height + 3, data)
                current_x += width
            
            current_y -= row_height
        
        return y_offset - current_y + 20
    
    def _draw_assembly_instructions(self, pdf_canvas: canvas.Canvas, template: Template,
                                  cuts: List[Cut], x_offset: float, y_offset: float,
                                  max_width: float) -> float:
        """Draw assembly instructions."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size + 1)
        pdf_canvas.drawString(x_offset, y_offset, "Assembly Instructions:")
        
        current_y = y_offset - 20
        
        instructions = [
            "1. Gather all pieces listed in the breakdown table above",
            "2. Verify each piece matches the template requirements",
            "3. Check cut quality and smooth any rough edges if needed",
            "4. Assemble pieces according to the template layout diagram",
            "5. Use appropriate fasteners for the installation location",
            "6. Ensure proper alignment and spacing during installation"
        ]
        
        pdf_canvas.setFont("Helvetica", self.font_size)
        line_height = 14
        
        for instruction in instructions:
            # Word wrap if instruction is too long
            if pdf_canvas.stringWidth(instruction, "Helvetica", self.font_size) > max_width:
                words = instruction.split()
                lines = []
                current_line = ""
                
                for word in words:
                    test_line = current_line + " " + word if current_line else word
                    if pdf_canvas.stringWidth(test_line, "Helvetica", self.font_size) <= max_width:
                        current_line = test_line
                    else:
                        if current_line:
                            lines.append(current_line)
                        current_line = word
                
                if current_line:
                    lines.append(current_line)
                
                for line in lines:
                    pdf_canvas.drawString(x_offset, current_y, line)
                    current_y -= line_height
            else:
                pdf_canvas.drawString(x_offset, current_y, instruction)
                current_y -= line_height
        
        return y_offset - current_y + 20
