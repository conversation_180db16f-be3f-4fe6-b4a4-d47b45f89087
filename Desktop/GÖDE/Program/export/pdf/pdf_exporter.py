"""
Main PDF exporter for drywall cutting reports.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
from datetime import datetime
import math

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4, letter
from reportlab.lib.units import mm, inch
from reportlab.lib.colors import black, blue, red, green, gray
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle
from reportlab.platypus.flowables import PageBreak
from shapely.geometry import Polygon

from core.geometry.shapes import Template, Sheet, Cut
from core.cutting.metadata import ProjectMetrics, CutMetrics
from utils.units import UnitConverter

logger = logging.getLogger(__name__)


class PDFExporter:
    """
    Main PDF exporter for generating cutting reports.
    
    Features:
    - Sheet cutting reports with visual layouts
    - Template assembly reports
    - Project summary reports
    - Configurable page layouts and styling
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize PDF exporter.
        
        Args:
            config: Configuration dictionary with PDF settings
        """
        self.config = config or {}
        
        # PDF settings from config
        pdf_config = self.config.get('pdf', {})
        self.page_size = self._get_page_size(pdf_config.get('page_size', 'A4'))
        self.margin_mm = pdf_config.get('margin_mm', 20)
        self.line_width = pdf_config.get('line_width', 0.5)
        self.font_size = pdf_config.get('font_size', 10)
        self.show_dimensions = pdf_config.get('show_dimensions', True)
        self.show_cut_ids = pdf_config.get('show_cut_ids', True)
        
        # Calculate usable area
        self.margin = self.margin_mm * mm
        self.page_width, self.page_height = self.page_size
        self.content_width = self.page_width - 2 * self.margin
        self.content_height = self.page_height - 2 * self.margin
        
        logger.info(f"PDFExporter initialized with page size {self.page_size}")
    
    def _get_page_size(self, size_name: str) -> Tuple[float, float]:
        """Get page size from name."""
        sizes = {
            'A4': A4,
            'A3': (A4[1], A4[0] * math.sqrt(2)),
            'Letter': letter
        }
        return sizes.get(size_name, A4)
    
    def export_project_report(self, output_path: str, templates: List[Template], 
                            sheets: List[Sheet], cuts: List[Cut],
                            project_metrics: ProjectMetrics) -> bool:
        """
        Export comprehensive project report.
        
        Args:
            output_path: Path to save PDF file
            templates: List of templates in project
            sheets: List of sheets (including remainders)
            cuts: List of cut operations
            project_metrics: Project performance metrics
            
        Returns:
            True if export successful
        """
        try:
            logger.info(f"Exporting project report to {output_path}")
            
            # Create PDF document
            doc = SimpleDocTemplate(
                output_path,
                pagesize=self.page_size,
                leftMargin=self.margin,
                rightMargin=self.margin,
                topMargin=self.margin,
                bottomMargin=self.margin
            )
            
            # Build content
            story = []
            
            # Title page
            story.extend(self._create_title_page(project_metrics))
            story.append(PageBreak())
            
            # Project summary
            story.extend(self._create_project_summary(project_metrics, templates, sheets, cuts))
            story.append(PageBreak())
            
            # Sheet cutting reports
            story.extend(self._create_sheet_reports(sheets, cuts))
            
            # Template assembly reports
            if cuts:
                story.append(PageBreak())
                story.extend(self._create_assembly_reports(templates, cuts))
            
            # Build PDF
            doc.build(story)
            
            logger.info(f"Successfully exported project report to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to export project report: {e}")
            return False
    
    def _create_title_page(self, metrics: ProjectMetrics) -> List[Any]:
        """Create title page content."""
        styles = getSampleStyleSheet()
        story = []
        
        # Title
        title = Paragraph("Drywall Cutting Report", styles['Title'])
        story.append(title)
        story.append(Spacer(1, 20))
        
        # Date
        date_text = f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        date_para = Paragraph(date_text, styles['Normal'])
        story.append(date_para)
        story.append(Spacer(1, 30))
        
        # Quick stats
        stats_data = [
            ['Total Cuts:', str(metrics.total_cuts)],
            ['Sheets Used:', str(metrics.sheets_used)],
            ['Templates Cut:', str(metrics.templates_cut)],
            ['Material Efficiency:', f"{metrics.material_efficiency:.1f}%"],
            ['Average Efficiency:', f"{metrics.average_efficiency:.1f}%"]
        ]
        
        stats_table = Table(stats_data, colWidths=[120, 80])
        stats_table.setStyle(TableStyle([
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
        ]))
        
        story.append(stats_table)
        
        return story
    
    def _create_project_summary(self, metrics: ProjectMetrics, templates: List[Template],
                              sheets: List[Sheet], cuts: List[Cut]) -> List[Any]:
        """Create project summary section."""
        styles = getSampleStyleSheet()
        story = []
        
        # Section title
        title = Paragraph("Project Summary", styles['Heading1'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Metrics table
        metrics_data = [
            ['Metric', 'Value', 'Unit'],
            ['Total Cut Area', f"{metrics.total_cut_area_mm2:.0f}", 'mm²'],
            ['Total Waste Area', f"{metrics.total_waste_area_mm2:.0f}", 'mm²'],
            ['Total Cut Length', f"{metrics.total_cut_length_mm:.0f}", 'mm'],
            ['Average Utilization', f"{metrics.average_utilization*100:.1f}", '%'],
            ['Processing Time', f"{metrics.total_processing_time_ms:.1f}", 'ms'],
        ]
        
        metrics_table = Table(metrics_data, colWidths=[120, 80, 40])
        metrics_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), gray),
            ('TEXTCOLOR', (0, 0), (-1, 0), black),
            ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTNAME', (0, 1), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 10),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 6),
            ('GRID', (0, 0), (-1, -1), 1, black),
        ]))
        
        story.append(metrics_table)
        story.append(Spacer(1, 20))
        
        # Templates summary
        if templates:
            story.append(Paragraph("Templates", styles['Heading2']))
            story.append(Spacer(1, 6))
            
            template_data = [['Template ID', 'Area (mm²)', 'Type']]
            for template in templates[:10]:  # Show first 10
                template_type = template.metadata.get('name', 'Unknown')
                template_data.append([
                    template.id[:8] + '...',
                    f"{template.area:.0f}",
                    template_type
                ])
            
            if len(templates) > 10:
                template_data.append(['...', f"({len(templates)-10} more)", ''])
            
            template_table = Table(template_data, colWidths=[100, 80, 80])
            template_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), gray),
                ('GRID', (0, 0), (-1, -1), 1, black),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 9),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ]))
            
            story.append(template_table)
        
        return story
    
    def _create_sheet_reports(self, sheets: List[Sheet], cuts: List[Cut]) -> List[Any]:
        """Create sheet cutting reports."""
        styles = getSampleStyleSheet()
        story = []
        
        # Section title
        title = Paragraph("Sheet Cutting Reports", styles['Heading1'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Group cuts by sheet
        cuts_by_sheet = {}
        for cut in cuts:
            if cut.sheet_id not in cuts_by_sheet:
                cuts_by_sheet[cut.sheet_id] = []
            cuts_by_sheet[cut.sheet_id].append(cut)
        
        # Create report for each sheet that was cut
        for sheet_id, sheet_cuts in cuts_by_sheet.items():
            # Find the original sheet
            original_sheet = None
            for sheet in sheets:
                if sheet.id == sheet_id:
                    original_sheet = sheet
                    break
            
            if original_sheet:
                story.extend(self._create_single_sheet_report(original_sheet, sheet_cuts))
                story.append(Spacer(1, 20))
        
        return story
    
    def _create_single_sheet_report(self, sheet: Sheet, cuts: List[Cut]) -> List[Any]:
        """Create report for a single sheet."""
        styles = getSampleStyleSheet()
        story = []
        
        # Sheet header
        sheet_name = sheet.metadata.get('name', f'Sheet {sheet.id[:8]}')
        header = Paragraph(f"Sheet: {sheet_name}", styles['Heading2'])
        story.append(header)
        story.append(Spacer(1, 6))
        
        # Sheet info
        info_data = [
            ['Sheet ID:', sheet.id[:8] + '...'],
            ['Dimensions:', f"{sheet.width:.0f} × {sheet.height:.0f} mm"],
            ['Area:', f"{sheet.area:.0f} mm²"],
            ['Cuts Made:', str(len(cuts))]
        ]
        
        info_table = Table(info_data, colWidths=[80, 120])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))
        
        story.append(info_table)
        story.append(Spacer(1, 12))
        
        # Cuts table
        if cuts:
            cuts_data = [['Cut ID', 'Template ID', 'Area (mm²)', 'Efficiency (%)']]
            for cut in cuts:
                efficiency = cut.metadata.get('sheet_utilization', 0) * 100
                cuts_data.append([
                    cut.id[:8] + '...',
                    cut.template_id[:8] + '...',
                    f"{cut.cut_area:.0f}",
                    f"{efficiency:.1f}"
                ])
            
            cuts_table = Table(cuts_data, colWidths=[80, 80, 70, 70])
            cuts_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), gray),
                ('GRID', (0, 0), (-1, -1), 1, black),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ]))
            
            story.append(cuts_table)
        
        return story
    
    def _create_assembly_reports(self, templates: List[Template], cuts: List[Cut]) -> List[Any]:
        """Create template assembly reports."""
        styles = getSampleStyleSheet()
        story = []
        
        # Section title
        title = Paragraph("Template Assembly Reports", styles['Heading1'])
        story.append(title)
        story.append(Spacer(1, 12))
        
        # Group cuts by template
        cuts_by_template = {}
        for cut in cuts:
            if cut.template_id not in cuts_by_template:
                cuts_by_template[cut.template_id] = []
            cuts_by_template[cut.template_id].append(cut)
        
        # Create assembly report for each template
        for template_id, template_cuts in cuts_by_template.items():
            # Find the template
            template = None
            for t in templates:
                if t.id == template_id:
                    template = t
                    break
            
            if template:
                story.extend(self._create_single_assembly_report(template, template_cuts))
                story.append(Spacer(1, 15))
        
        return story
    
    def _create_single_assembly_report(self, template: Template, cuts: List[Cut]) -> List[Any]:
        """Create assembly report for a single template."""
        styles = getSampleStyleSheet()
        story = []
        
        # Template header
        template_name = template.metadata.get('name', f'Template {template.id[:8]}')
        header = Paragraph(f"Template: {template_name}", styles['Heading3'])
        story.append(header)
        story.append(Spacer(1, 6))
        
        # Template info and cuts
        info_data = [
            ['Template ID:', template.id[:8] + '...'],
            ['Area:', f"{template.area:.0f} mm²"],
            ['Instances Cut:', str(len(cuts))]
        ]
        
        info_table = Table(info_data, colWidths=[80, 100])
        info_table.setStyle(TableStyle([
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
        ]))
        
        story.append(info_table)
        
        # Cut instances
        if len(cuts) > 1:
            story.append(Spacer(1, 6))
            instances_data = [['Instance', 'Sheet ID', 'Cut Length (mm)']]
            for i, cut in enumerate(cuts, 1):
                instances_data.append([
                    str(i),
                    cut.sheet_id[:8] + '...',
                    f"{cut.total_cut_length:.0f}"
                ])
            
            instances_table = Table(instances_data, colWidths=[50, 80, 80])
            instances_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), gray),
                ('GRID', (0, 0), (-1, -1), 1, black),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 8),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
            ]))
            
            story.append(instances_table)
        
        return story
