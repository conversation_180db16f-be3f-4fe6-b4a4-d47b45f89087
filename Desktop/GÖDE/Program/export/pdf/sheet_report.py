"""
Sheet cutting report generator with visual layouts.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
import math

from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.lib.colors import black, blue, red, green, gray
from reportlab.lib.colors import Color

# Define lightgray if not available
try:
    from reportlab.lib.colors import lightgray
except ImportError:
    lightgray = Color(0.9, 0.9, 0.9)
from shapely.geometry import Polygon

from core.geometry.shapes import Sheet, Cut, Template
from utils.units import UnitConverter

logger = logging.getLogger(__name__)


class SheetReportGenerator:
    """
    Generates visual sheet cutting reports with annotated layouts.
    
    Features:
    - Visual representation of sheet with cut lines
    - Dimension annotations
    - Cut ID labels
    - Remainder piece highlighting
    - Scale-to-fit layout
    """
    
    def __init__(self, config: Optional[Dict[str, Any]] = None):
        """
        Initialize sheet report generator.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config or {}
        
        # Drawing settings
        self.margin = 20 * mm
        self.line_width = 0.5
        self.font_size = 8
        self.dimension_font_size = 7
        
        # Colors
        self.sheet_color = lightgray
        self.cut_line_color = red
        self.template_color = blue
        self.dimension_color = black
        
        logger.info("SheetReportGenerator initialized")
    
    def generate_sheet_report(self, pdf_canvas: canvas.Canvas, sheet: Sheet, 
                            cuts: List[Cut], templates: List[Template],
                            x_offset: float = 0, y_offset: float = 0,
                            max_width: float = None, max_height: float = None) -> Tuple[float, float]:
        """
        Generate visual sheet cutting report on PDF canvas.
        
        Args:
            pdf_canvas: ReportLab canvas to draw on
            sheet: Sheet to visualize
            cuts: List of cuts made on this sheet
            templates: List of templates for reference
            x_offset, y_offset: Position offset on canvas
            max_width, max_height: Maximum dimensions for scaling
            
        Returns:
            Tuple of (actual_width, actual_height) used
        """
        try:
            # Calculate scale to fit in available space
            if max_width is None:
                max_width = pdf_canvas._pagesize[0] - 2 * self.margin
            if max_height is None:
                max_height = pdf_canvas._pagesize[1] - 2 * self.margin
            
            scale = self._calculate_scale(sheet, max_width, max_height)
            
            # Calculate actual dimensions
            sheet_width = sheet.width * scale
            sheet_height = sheet.height * scale
            
            # Draw sheet outline
            self._draw_sheet_outline(pdf_canvas, x_offset, y_offset, sheet_width, sheet_height)
            
            # Draw cut lines and templates
            for cut in cuts:
                template = self._find_template_for_cut(cut, templates)
                if template:
                    self._draw_cut_visualization(pdf_canvas, cut, template, 
                                               x_offset, y_offset, scale)
            
            # Draw dimensions
            if self.config.get('show_dimensions', True):
                self._draw_dimensions(pdf_canvas, sheet, x_offset, y_offset, 
                                    sheet_width, sheet_height)
            
            # Draw cut IDs
            if self.config.get('show_cut_ids', True):
                self._draw_cut_ids(pdf_canvas, cuts, templates, 
                                 x_offset, y_offset, scale)
            
            # Draw title
            self._draw_sheet_title(pdf_canvas, sheet, x_offset, y_offset + sheet_height + 10)
            
            return sheet_width, sheet_height
            
        except Exception as e:
            logger.error(f"Failed to generate sheet report: {e}")
            return 0, 0
    
    def _calculate_scale(self, sheet: Sheet, max_width: float, max_height: float) -> float:
        """Calculate scale factor to fit sheet in available space."""
        scale_x = max_width / sheet.width
        scale_y = max_height / sheet.height
        
        # Use smaller scale to ensure it fits
        scale = min(scale_x, scale_y)
        
        # Limit maximum scale to avoid tiny sheets becoming huge
        max_scale = 2.0  # Maximum 2x scale
        scale = min(scale, max_scale)
        
        return scale
    
    def _draw_sheet_outline(self, pdf_canvas: canvas.Canvas, x: float, y: float, 
                          width: float, height: float):
        """Draw the sheet outline."""
        pdf_canvas.setStrokeColor(black)
        pdf_canvas.setFillColor(self.sheet_color)
        pdf_canvas.setLineWidth(self.line_width * 2)
        
        # Draw filled rectangle for sheet
        pdf_canvas.rect(x, y, width, height, fill=1, stroke=1)
    
    def _draw_cut_visualization(self, pdf_canvas: canvas.Canvas, cut: Cut, template: Template,
                              x_offset: float, y_offset: float, scale: float):
        """Draw visualization of a cut operation."""
        try:
            # Get cut polygon coordinates
            coords = list(cut.cut_polygon.exterior.coords)
            
            # Scale and offset coordinates
            scaled_coords = []
            for coord_x, coord_y in coords:
                scaled_x = x_offset + coord_x * scale
                scaled_y = y_offset + coord_y * scale
                scaled_coords.append((scaled_x, scaled_y))
            
            # Draw template area
            pdf_canvas.setFillColor(self.template_color)
            pdf_canvas.setStrokeColor(self.cut_line_color)
            pdf_canvas.setLineWidth(self.line_width)
            
            # Create path for polygon
            if len(scaled_coords) >= 3:
                path = pdf_canvas.beginPath()
                path.moveTo(scaled_coords[0][0], scaled_coords[0][1])
                
                for coord_x, coord_y in scaled_coords[1:]:
                    path.lineTo(coord_x, coord_y)
                
                path.close()
                pdf_canvas.drawPath(path, fill=1, stroke=1)
            
            # Draw cut lines
            self._draw_cut_lines(pdf_canvas, cut, x_offset, y_offset, scale)
            
        except Exception as e:
            logger.warning(f"Failed to draw cut visualization: {e}")
    
    def _draw_cut_lines(self, pdf_canvas: canvas.Canvas, cut: Cut,
                       x_offset: float, y_offset: float, scale: float):
        """Draw the actual cut lines."""
        pdf_canvas.setStrokeColor(self.cut_line_color)
        pdf_canvas.setLineWidth(self.line_width * 2)
        
        for line_segment in cut.cut_lines:
            start_x = x_offset + line_segment.start.x * scale
            start_y = y_offset + line_segment.start.y * scale
            end_x = x_offset + line_segment.end.x * scale
            end_y = y_offset + line_segment.end.y * scale
            
            pdf_canvas.line(start_x, start_y, end_x, end_y)
    
    def _draw_dimensions(self, pdf_canvas: canvas.Canvas, sheet: Sheet,
                        x_offset: float, y_offset: float, 
                        sheet_width: float, sheet_height: float):
        """Draw dimension annotations."""
        pdf_canvas.setStrokeColor(self.dimension_color)
        pdf_canvas.setFillColor(self.dimension_color)
        pdf_canvas.setFont("Helvetica", self.dimension_font_size)
        
        # Width dimension (bottom)
        dim_y = y_offset - 15
        pdf_canvas.line(x_offset, dim_y, x_offset + sheet_width, dim_y)
        pdf_canvas.line(x_offset, dim_y - 3, x_offset, dim_y + 3)
        pdf_canvas.line(x_offset + sheet_width, dim_y - 3, x_offset + sheet_width, dim_y + 3)
        
        width_text = f"{sheet.width:.0f} mm"
        text_width = pdf_canvas.stringWidth(width_text, "Helvetica", self.dimension_font_size)
        pdf_canvas.drawString(x_offset + (sheet_width - text_width) / 2, dim_y - 12, width_text)
        
        # Height dimension (left)
        dim_x = x_offset - 15
        pdf_canvas.line(dim_x, y_offset, dim_x, y_offset + sheet_height)
        pdf_canvas.line(dim_x - 3, y_offset, dim_x + 3, y_offset)
        pdf_canvas.line(dim_x - 3, y_offset + sheet_height, dim_x + 3, y_offset + sheet_height)
        
        height_text = f"{sheet.height:.0f} mm"
        
        # Rotate text for vertical dimension
        pdf_canvas.saveState()
        pdf_canvas.translate(dim_x - 8, y_offset + sheet_height / 2)
        pdf_canvas.rotate(90)
        text_width = pdf_canvas.stringWidth(height_text, "Helvetica", self.dimension_font_size)
        pdf_canvas.drawString(-text_width / 2, 0, height_text)
        pdf_canvas.restoreState()
    
    def _draw_cut_ids(self, pdf_canvas: canvas.Canvas, cuts: List[Cut], templates: List[Template],
                     x_offset: float, y_offset: float, scale: float):
        """Draw cut ID labels on the visualization."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size)
        
        for cut in cuts:
            try:
                # Get centroid of cut polygon
                centroid = cut.cut_polygon.centroid
                
                # Scale and position
                label_x = x_offset + centroid.x * scale
                label_y = y_offset + centroid.y * scale
                
                # Draw cut ID (shortened)
                cut_id_short = cut.id[:6] + "..."
                text_width = pdf_canvas.stringWidth(cut_id_short, "Helvetica-Bold", self.font_size)
                
                # Draw background rectangle for better readability
                pdf_canvas.setFillColor(lightgray)
                pdf_canvas.rect(label_x - text_width/2 - 2, label_y - 3, 
                              text_width + 4, self.font_size + 2, fill=1, stroke=0)
                
                # Draw text
                pdf_canvas.setFillColor(black)
                pdf_canvas.drawString(label_x - text_width/2, label_y, cut_id_short)
                
            except Exception as e:
                logger.warning(f"Failed to draw cut ID for cut {cut.id}: {e}")
    
    def _draw_sheet_title(self, pdf_canvas: canvas.Canvas, sheet: Sheet,
                         x_offset: float, y_offset: float):
        """Draw sheet title and information."""
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size + 2)
        
        # Sheet name
        sheet_name = sheet.metadata.get('name', f'Sheet {sheet.id[:8]}...')
        pdf_canvas.drawString(x_offset, y_offset, sheet_name)
        
        # Sheet info
        pdf_canvas.setFont("Helvetica", self.font_size)
        info_text = f"ID: {sheet.id[:8]}... | {sheet.width:.0f} × {sheet.height:.0f} mm | Area: {sheet.area:.0f} mm²"
        pdf_canvas.drawString(x_offset, y_offset - 12, info_text)
        
        # Remainder indicator
        if sheet.is_remainder:
            pdf_canvas.setFillColor(red)
            pdf_canvas.drawString(x_offset, y_offset - 24, "REMAINDER PIECE")
            pdf_canvas.setFillColor(black)
    
    def _find_template_for_cut(self, cut: Cut, templates: List[Template]) -> Optional[Template]:
        """Find the template associated with a cut."""
        for template in templates:
            if template.id == cut.template_id:
                return template
        return None
    
    def generate_remainder_visualization(self, pdf_canvas: canvas.Canvas, 
                                       remainder_sheets: List[Sheet],
                                       x_offset: float, y_offset: float,
                                       max_width: float, max_height: float) -> Tuple[float, float]:
        """
        Generate visualization of remainder pieces.
        
        Args:
            pdf_canvas: ReportLab canvas
            remainder_sheets: List of remainder pieces
            x_offset, y_offset: Position offset
            max_width, max_height: Maximum dimensions
            
        Returns:
            Tuple of (width_used, height_used)
        """
        if not remainder_sheets:
            return 0, 0
        
        try:
            # Calculate layout for remainder pieces
            layout = self._calculate_remainder_layout(remainder_sheets, max_width, max_height)
            
            current_x = x_offset
            current_y = y_offset
            max_row_height = 0
            total_width = 0
            total_height = 0
            
            for i, (sheet, scale, width, height) in enumerate(layout):
                # Check if we need to start a new row
                if current_x + width > x_offset + max_width and i > 0:
                    current_x = x_offset
                    current_y += max_row_height + 10
                    total_height += max_row_height + 10
                    max_row_height = 0
                
                # Draw remainder piece
                self._draw_remainder_piece(pdf_canvas, sheet, current_x, current_y, scale)
                
                # Update positions
                current_x += width + 10
                max_row_height = max(max_row_height, height)
                total_width = max(total_width, current_x - x_offset)
            
            total_height += max_row_height
            
            return total_width, total_height
            
        except Exception as e:
            logger.error(f"Failed to generate remainder visualization: {e}")
            return 0, 0
    
    def _calculate_remainder_layout(self, remainder_sheets: List[Sheet], 
                                  max_width: float, max_height: float) -> List[Tuple[Sheet, float, float, float]]:
        """Calculate layout for remainder pieces."""
        layout = []
        
        for sheet in remainder_sheets:
            # Calculate scale for individual piece
            piece_max_width = min(max_width / 3, 150)  # Max 1/3 of available width
            piece_max_height = min(max_height / 3, 150)  # Max 1/3 of available height
            
            scale = self._calculate_scale(sheet, piece_max_width, piece_max_height)
            width = sheet.width * scale
            height = sheet.height * scale
            
            layout.append((sheet, scale, width, height))
        
        return layout
    
    def _draw_remainder_piece(self, pdf_canvas: canvas.Canvas, sheet: Sheet,
                            x_offset: float, y_offset: float, scale: float):
        """Draw a single remainder piece."""
        width = sheet.width * scale
        height = sheet.height * scale
        
        # Draw outline
        pdf_canvas.setStrokeColor(red)
        pdf_canvas.setFillColor(lightgray)
        pdf_canvas.setLineWidth(self.line_width)
        pdf_canvas.rect(x_offset, y_offset, width, height, fill=1, stroke=1)
        
        # Draw label
        pdf_canvas.setFillColor(red)
        pdf_canvas.setFont("Helvetica-Bold", self.font_size - 1)
        
        label = f"R-{sheet.id[:4]}"
        text_width = pdf_canvas.stringWidth(label, "Helvetica-Bold", self.font_size - 1)
        pdf_canvas.drawString(x_offset + (width - text_width) / 2, 
                            y_offset + height / 2, label)
        
        # Draw dimensions
        pdf_canvas.setFillColor(black)
        pdf_canvas.setFont("Helvetica", self.font_size - 2)
        dim_text = f"{sheet.width:.0f}×{sheet.height:.0f}"
        dim_width = pdf_canvas.stringWidth(dim_text, "Helvetica", self.font_size - 2)
        pdf_canvas.drawString(x_offset + (width - dim_width) / 2, 
                            y_offset - 10, dim_text)
